
<div class="card">
    <div class="card-header">User Action</div>
    <div class="card-body p-0">
        <?php if($school_contact_info->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col" style="text-align: left;">Job Title</th>
                            <th scope="col" style="text-align: left;">Name</th>
                            <th scope="col" style="text-align: left;">Email</th>
                            <th scope="col" style="text-align: left;">Phone</th>
                            <th scope="col" style="text-align: left;">Roles</th>
                            <th scope="col" style="text-align: left;">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = @$school_contact_info; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($data->job_title); ?></td>
                                <td><?php echo e($data->first_name); ?> <?php echo e($data->last_name); ?></td>
                                <td><?php echo e($data->email); ?></td>
                                <td><?php echo e($data->phone_number); ?> </td>
                                <td>
                                    <select name="role_id" class="form-control role-select" data-original-role="<?php echo e($data->role_id); ?>" data-user-id="<?php echo e($data->id); ?>" id="role_id_<?php echo e($data->id); ?>">
                                        <?php $__currentLoopData = $schoolRoles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($role->id); ?>" <?php echo e($data->role_id == $role->id ? 'selected' : ''); ?>>
                                                <?php echo e($role->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </td>
                                <td>
                                    <a href="javascript:void(0)" class="btn btn-primary btn-block sendcred" onclick="sendCredentials('<?php echo e(route('admin.sendCredentialsPlatformSchools')); ?>', '<?php echo e($data->id); ?>')">Send Credentials</a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-muted m-3">No invites sent yet.</p>
        <?php endif; ?>
    </div>
</div>
<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        $('.role-select').change(function() {
            var userId = $(this).data('user-id');
            var roleId = $(this).val();
            // var $select = $(this);

            // if ($select.data('original-role') == 1 && roleId != 1) {
            //     alertify.error("You cannot change the Admin role");
            //     $select.val("1");
            //     return false;
            // }

            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: "<?php echo e(route('admin.marketplace-updateSchoolUserRoles')); ?>",
                type: "POST",
                data: {
                    user_id: userId,
                    role_id: roleId
                },
                success: function(response) {
                    if (response.success) {
                        alertify.success(response.message);
                    } else {
                        alertify.error(response.message);
                    }
                }
            });
        });
    });
</script>
<script>
    function sendCredentials(url, id) {
        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            type: 'POST',
            url: url,
            data: { id: id },
            dataType: "json",
            success: function (data) {
                if (data.success) {
                    alertify.success(data.message);
                } else {
                    alertify.error(data.message);
                }
            }
        });
    }
</script>
<?php $__env->stopSection(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/schools/tabs/user-action.blade.php ENDPATH**/ ?>