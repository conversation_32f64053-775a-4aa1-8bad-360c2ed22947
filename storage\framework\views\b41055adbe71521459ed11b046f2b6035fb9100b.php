<?php if(request()->segment(4) == 'step8'): ?>
<div class="card-body p-2 px-3">

    <?php $__currentLoopData = $data['sample_lesson']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

    <li class="list-group-item d-flex p-0 border-0">
        <ul class="list-group list-group-flush w-100 mx-2 my-3 border border-light rounded">
            <li class="list-group-item d-flex">
                <span class="col-lg-3 col-md-3 col-4 pl-0">Subject Name :</span>
                <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                    <?php if(!empty($lesson->subject_id)): ?>
                    <?php echo e(v1SubjectName($lesson->subject_id)); ?>

                    <?php endif; ?>
                </h6>
                <!-- <?php if(!empty($educations->credentialing_agency) && !empty($educations->credentialing_agency_other)): ?>
                <span class="col-lg-3 col-md-3 col-4 pl-0">Other Credentialing Agency:</span>
                <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                    <?php echo e($educations->credentialing_agency_other); ?>

                </h6>
                <?php endif; ?> -->
            </li>
            <li class="list-group-item d-flex">
                <span class="col-lg-3 col-md-3 col-4 pl-0">File :</span>
                <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                    <?php if(!empty($lesson->file_url)): ?>
                    <a target="_blank" href="<?php echo e(generateSignedUrl($lesson->file_url)); ?>">
                        <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                    </a>
                    <?php endif; ?>
                </h6>

            </li>
            <li class="list-group-item d-flex">
                <span class="col-lg-3 col-md-3 col-4 pl-0">Additonal Notes :</span>
                <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                    <?php if(!empty($lesson->additional_notes)): ?>
                   <?php echo e($lesson->additional_notes); ?>

                    <?php endif; ?>
                </h6>

            </li>






        </ul>
    </li>


    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



</div>


<?php endif; ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/application-steps/step-8.blade.php ENDPATH**/ ?>