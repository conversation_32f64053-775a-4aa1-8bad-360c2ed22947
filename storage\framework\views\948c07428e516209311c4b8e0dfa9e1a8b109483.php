
<?php $__env->startSection('title'); ?>Applied Requests List | Whizara <?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<style>
    th{
        text-align: left;
    }
    .truncate-text {
        display: inline-block;
        max-width: 150px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
        cursor: pointer;
    }
</style>

<main class="content">
    <div class="container-fluid p-0">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item " aria-current="page">Manage Marketplace</li>
                <li class="breadcrumb-item " aria-current="page">Applied Requests List</li>
            </ol>
        </nav>

        <div class="table-responsive" id="applied-requests-table">
            <table class="table table-striped admin-dataTable" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <th>Requirement</th>
                        <th>Educator Name</th>
                        <th>Educator Email</th>
                        <th>Requirement Start Date</th>
                        <th>Invite ID</th>
                        <th>Offer Description</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(!empty($appliedRequests) && $appliedRequests->count()): ?>
                        <?php $__currentLoopData = $appliedRequests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $invite): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="text-center"><a href="<?php echo e(url('admin/k12connections/view-requirements/'.encrypt_str($invite->requirement->id))); ?>"><?php echo e($invite->requirement->id); ?></a></td>
                                <td><a href="<?php echo e(url('admin/k12connections/manage-educator', $invite->user->id)); ?>"><?php echo e($invite->user->first_name); ?> <?php echo e($invite->user->last_name); ?></a></td>
                                <td><?php echo e($invite->user->email); ?></td>
                                <td><?php echo e($invite->requirement->start_date); ?></td>
                                <td><?php echo e($invite->id); ?></td>
                                <td>
                                    <span class="truncate-text" title="<?php echo e($invite->offer_description); ?>">
                                        <?php echo e($invite->offer_description); ?>

                                    </span>
                                </td>
                                <td>
                                    <select name="status" id="status" class="form-control" data-inviteId="<?php echo e($invite->id); ?>" data-field="status" onchange="changeInviteStatus('<?php echo e(route('admin.marketplace-updateAppliedRequestStatus')); ?>', '<?php echo e($invite->id); ?>', this.value)">
                                        <option value="pending" <?php echo e($invite->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                        <option value="accepted" <?php echo e($invite->status == 'accepted' ? 'selected' : ''); ?>>Accepted</option>
                                        <option value="rejected" <?php echo e($invite->status == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                                        <option value="withdraw" <?php echo e($invite->status == 'withdraw' ? 'selected' : ''); ?>>Withdrawn</option>
                                    </select>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" <?php if($invite->status != 'pending'): ?> disabled <?php endif; ?> onclick="withdrawInvite('<?php echo e(route('admin.marketplace-updateAppliedRequestStatus')); ?>', '<?php echo e($invite->id); ?>')">Withdraw</button>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="10">No data found.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        

        
    </div>
</main>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Change Invite Status
    async function changeInviteStatus(url, id, status) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: status
                }
            });
            if (response) {
                alertify.success(response.message);
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }

    // Withdraw Invite
    async function withdrawInvite(url, id) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: 'withdraw'
                }
            });

            if (response) {
                alertify.success(response.message);
                $(`[data-inviteId="${id}"data-field="status"]`).html('Withdrawn');
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/manage-requests/appliedrequests.blade.php ENDPATH**/ ?>