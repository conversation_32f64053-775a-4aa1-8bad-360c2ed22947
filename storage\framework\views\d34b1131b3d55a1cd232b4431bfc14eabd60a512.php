<?php if(request()->segment(4) == 'step4'): ?>
    <div class="card-body  p-2 px-3">
        <ul class="list-group list-group-flush">
            <?php if(!empty($data['user_fourth_step']) && count($data['user_fourth_step']) > 0): ?>
                <?php $__currentLoopData = $data['user_fourth_step']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Grade Levels :</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                            <?php echo e(!empty($step->i_prefer_to_teach) ? gradeLevel($step->i_prefer_to_teach) : ''); ?>

                        </h6>
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Delivery Mode :</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                            <?php echo e(!empty($step->format) ? $step->format : ''); ?>

                        </h6>
                    </li>
                    
                    <?php if(count($step->subjects) > 0): ?>
                        <?php $__currentLoopData = $step->subjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rows): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="list-group-item d-flex">
                                <span class="col-lg-3 col-md-3 col-4 pl-0">Subjects :</span>
                                <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                                    <?php if($rows->subject != 'Other'): ?>
                                        <?php echo e(v1SubjectAreaName($rows->subject)); ?>

                                        (<?php echo e(v1SubjectName($rows->sub_subject)); ?>)
                                    <?php else: ?>
                                        <?php echo e('Other'); ?>

                                        (<?php echo e($rows->other); ?>)
                                    <?php endif; ?>
                                </h6>
                                <span class="col-lg-3 col-md-3 col-4 pl-0">Proficiency :</span>
                                <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                                    <?php echo e($rows->proficiency); ?>

                                </h6>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Languages :</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                            <?php echo e(!empty($step->language_teach_that_i_teach) ? $step->language_teach_that_i_teach : ''); ?>

                        </h6>
                        <?php if(!empty($step->language_teach_that_i_teach) && str_contains($step->language_teach_that_i_teach, 'Other')): ?>
                            <span class="col-lg-3 col-md-3 col-4 pl-0">Other Language:</span>
                            <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                                <?php echo e(!empty($step->other_language) ? $step->other_language : ''); ?>

                            </h6>
                        <?php endif; ?>
                    </li>
                    
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Program Type :</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                            <?php echo e(!empty($step->program_type) ? $step->program_type : ''); ?>

                        </h6>
                        <?php if(!empty($step->program_type) && str_contains($step->program_type, 'Other')): ?>
                            <span class="col-lg-3 col-md-3 col-4 pl-0">Other Program Type :</span>
                            <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                                <?php echo e(!empty($step->other_program_type) ? $step->other_program_type : ''); ?>

                            </h6>
                        <?php endif; ?>
                    </li>
                    <li class="list-group-item">
                        <p class="mb-3" style="font-weight: 600;">Curriculum Preference :</p>
                        <div class="row">
                            <div class="col-md-12 col-lg-12">
                                    <div class=" d-flex">
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">I will bring lesson plans :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                                            <?php echo e(!empty($step->curriculum) ? ucfirst($step->curriculum) : ''); ?>

                                        </h6>
                                    </div>
                                    <div class="form__check d-flex align-items-center ps-0 pt-3">
                                        <div class="form__item">
                                            <input type="checkbox" disabled <?php if(!empty($step->scope_sequence) && ($step->scope_sequence == 1)): ?> checked <?php endif; ?>
                                                name="scope_sequence" id="scope_sequence">
                                            <label for="scope_sequence">I am open to aligning with school provided scope and sequence</label>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </li>
                    <li class="list-group-item">
                        <?php
                            $for = !empty($step->format) ? explode(',', $step->format) : '';
                            $comp = !empty($step->compensation) ? $step->compensation : '';
                            $compArray = (in_array('hybrid', $for) && !empty($comp)) ? explode(',', $comp) : [$comp];
                        ?>
                        <p class="mb-3" style="font-weight: 600;">Delivery Mode :</p>
                        <div class="row">
                            <div class="form__check col-lg-9 ps-0">
                                <?php if(!empty($for) && in_array('in-person', $for)): ?>
                                    <div class="format-row row">
                                        <div class="col-lg-4 ps-0">
                                            <label>
                                                Mode : <?php echo e(Str::ucfirst('in-person')); ?>

                                            </label>
                                        </div>
                                        <?php if(!empty($comp) && !empty($comp)): ?>
                                            <div class="col-lg-8">
                                                <label>
                                                    Compensation : <?php echo e(in_array('hybrid', $for) && !empty($compArray[1]) ? $compArray[1] : $comp); ?>

                                                </label>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if(!empty($for) && in_array('online', $for)): ?>
                                    <div class="form__item">
                                        <div class="row">
                                            <div class="col-lg-4 ps-0">
                                                <label for="forgot">
                                                    Mode : <?php echo e(Str::ucfirst('online')); ?>

                                                </label>
                                            </div>
                                            <?php if(!empty($comp) && !empty($comp)): ?>
                                                <div class="col-lg-8">
                                                    <label for="forgot">
                                                        Compensation : <?php echo e(in_array('hybrid', $for) && !empty($compArray[0]) ? $compArray[0] : $comp); ?>

                                                    </label>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <?php if(!empty($for) && in_array('online', $for)): ?>
                                <div class="form__check col-lg-9 ps-0">
                                    <div class="form__item">
                                        <input type="checkbox" disabled <?php if(!empty($user->internet_connection == 1)): ?> checked <?php endif; ?>
                                            name="internet_connection" id="internet_connection">
                                        <label for="internet_connection">I have stable high speed internet
                                            connection</label>
                                    </div>
                                    <div class="form__item">
                                        <input type="checkbox" disabled <?php if(!empty($user->work_from_home_setup == 1)): ?> checked <?php endif; ?>
                                            name="work_from_home_setup" id="work_from_home_setup">
                                        <label for="work_from_home_setup">I have a work from home setup to ensure quiet and
                                            well
                                            lit space to conduct online classes</label>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <li class="list-group-item d-flex">No data found</li>
            <?php endif; ?>
        </ul>
    </div>
<?php endif; ?>
<?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/application-steps/step-4.blade.php ENDPATH**/ ?>