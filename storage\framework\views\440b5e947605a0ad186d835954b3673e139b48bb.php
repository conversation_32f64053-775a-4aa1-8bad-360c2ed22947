<!-- Invite <PERSON>ton -->
<div class="d-flex justify-content-end gap-2 align-items-center mb-3">
    <button type="button" class="mx-3 btn btn-primary"
        data-toggle="modal"
        data-target="#mainInstructorInvitePopup"
        id="send-invite"
        onclick="inviteMainInstructor('<?php echo e(route('admin.marketplace-requirementsInvite')); ?>', '<?php echo e(encrypt_str($data->id)); ?>')">
        Main Instructor Invite
    </button>
    <button type="button" class="btn btn-primary"
        data-toggle="modal"
        data-target="#standByInvitePopup"
        id="send-invite"
        onclick="inviteStandbyInstructor('<?php echo e(route('admin.marketplace-requirementsInvite')); ?>', '<?php echo e(encrypt_str($data->id)); ?>')">
        Standby Instructor Invite
    </button>
</div>

<!-- Invite History Table -->
<div class="card">
    <div class="card-header">
        Invite History
    </div>
    <div class="card-body p-0">
        <?php if($data->invites->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th>#</th>
                            <th>Educator</th>
                            <th>Email</th>
                            <th>Deadline</th>
                            <th>Invited At</th>
                            <th>Invited By</th>
                            <th>Invite Type</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $data->invites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $invite): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $isExpired = \Carbon\Carbon::parse($invite->deadline_date . ' ' . $invite->deadline_time)->isPast();
                                $status = $isExpired ? 'Expired' : ucfirst($invite->status ?? 'Pending');
                                $badgeClass = $isExpired ? 'dark' : ($invite->status === 'accepted' ? 'success' : 'info');
                            ?>
                            <tr>
                                <td><?php echo e($index + 1); ?></td>
                                <td data-inviteId="<?php echo e($invite->id); ?>" data-field="name"><a href="<?php echo e(url('admin/k12connections/manage-educator', $invite->educator->id)); ?>"><?php echo e($invite->educator->first_name); ?> <?php echo e($invite->educator->last_name); ?></a></td>
                                <td data-inviteId="<?php echo e($invite->id); ?>" data-field="email"><?php echo e($invite->educator->email); ?></td>
                                <td data-inviteId="<?php echo e($invite->id); ?>" data-field="deadline">
                                    <?php echo e(\Carbon\Carbon::parse($invite->deadline_date)->format('m-d-Y')); ?>

                                    <?php echo e(\Carbon\Carbon::parse($invite->deadline_time)->format('h:i A')); ?>

                                </td>
                                <td data-inviteId="<?php echo e($invite->id); ?>" data-field="created_at">
                                    <?php echo e(\Carbon\Carbon::parse($invite->create_at)->format('m-d-Y h:i A')); ?>

                                </td>
                                <td data-inviteId="<?php echo e($invite->id); ?>" data-field="invitedBy">
                                    <?php $invitedBy = \App\User::find($invite->invited_by); ?>
                                    <?php echo e($invitedBy->first_name ?? 'N/A'); ?> <?php echo e($invitedBy->last_name ?? ''); ?>

                                </td>
                                <td data-inviteId="<?php echo e($invite->id); ?>" data-field="type">
                                    <?php echo e($invite->type); ?>

                                </td>
                                <td data-inviteId="<?php echo e($invite->id); ?>" data-field="status">
                                    <span class="badge badge-<?php echo e($badgeClass); ?>"><?php echo e($status); ?></span>
                                </td>
                                <td data-inviteId="<?php echo e($invite->id); ?>" data-field="action">
                                    <button class="btn btn-sm btn-primary" <?php if($status != 'Pending'): ?> disabled <?php endif; ?> onclick="withdrawInvite('<?php echo e(route('admin.marketplace-updateInviteStatus')); ?>', '<?php echo e($invite->id); ?>')">Withdraw</button>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-muted m-3">No invites sent yet.</p>
        <?php endif; ?>
    </div>
</div>

<!-- Modal (only for sending invites) -->
<div class="modal" id="mainInstructorInvitePopup" tabindex="-1" aria-labelledby="mainInstructorInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>

<!-- Modal (only for sending invites) -->
<div class="modal" id="standByInvitePopup" tabindex="-1" aria-labelledby="standByInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>


<!-- Scripts moved to parent file -->
<?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/requirements/tabs/invitation.blade.php ENDPATH**/ ?>