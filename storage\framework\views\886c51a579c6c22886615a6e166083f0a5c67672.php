

<?php $__env->startSection('title'); ?>
Manage Educator | Whizara
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="table-responsive">
    <table class="table table-striped" style="width:100%" id="dataTable">
        <thead class="thead-dark">
            <tr>
                <th>User Id</th>
                <th>Name</th>
                <th>Email</th>
                <th>State</th>
                <th>City</th>
                <th>Subjects</th>
                <th>Substitute</th>
                <th>Availability</th>
                <th>Teaching Preference</th>
                <th>Format</th>
                <th>Availability Schedule</th>
                <th>Add More Subjects</th>
                <th>Add Substitutes</th>
                <th>Add Phone Number</th>
                <th>Allow Notifications</th>
                <th>Onboarding</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/datatables.min.css')); ?>">
<script src="<?php echo e(asset('js/datatables.min.js')); ?>"></script>
<script>
    $(function() {
        if (typeof dataTable !== 'undefined' && dataTable instanceof $.fn.dataTable.Api) {
            dataTable.destroy();
        }
        window.dataTable = initializeAdminDataTable("#dataTable",
            "<?php echo e(route('admin.manage-educator.index')); ?>", [{
                    data: 'user_id',
                    searchable: false,
                    visible: false,
                },
                {
                    data: 'name',
                },
                {
                    data: 'email',
                },
                {
                    data: 'state',
                },
                {
                    data: 'city',
                },
                {
                    data: 'subjects',
                },
                {
                    data: 'add_more_subjects',
                },
                {
                    data: 'availability_time',
                },
                {
                    data: 'teaching_preference',
                },
                {
                    data: 'format',
                },
                {
                    data: 'availability_schedule_status'
                },
                {
                    data: 'add_more_subjects_status'
                },
                {
                    data: 'add_substitutes_status'
                },
                {
                    data: 'add_phone_number_status'
                },
                {
                    data: 'allow_notifications_status'
                },
                {
                    data: 'status',
                },
                {
                    data: 'action',
                    name: 'actions',
                    orderable: false,
                    searchable: false,
                },
            ],
        );
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/educator/index.blade.php ENDPATH**/ ?>