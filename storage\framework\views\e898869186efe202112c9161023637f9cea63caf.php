

<?php $__env->startSection('title'); ?> View Platform School | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php $res=get_permission(session('Adminnewlogin')['type']); ?>
<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <?php if(isset($res['dashboard'])): ?>
                    <?php if(array_key_exists('dashboard',$res)): ?>
                        <?php if(in_array('add',json_decode($res['dashboard'] ,true))): ?>
                            <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if(isset($res['manageschool'])): ?>
                    <?php if(array_key_exists('manageschool',$res)): ?>
                        <?php if(in_array('view',json_decode($res['manageschool'] ,true))): ?>
                            <li class="breadcrumb-item active float-right" aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-platform-schools')); ?>"> List Platform School</a></li>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
                <li class="breadcrumb-item active" aria-current="page">View Platform School</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <!-- EDIT PROFILE SECTION START -->
        <div class="row justify-content-center">
            <div class="col-lg-2 col-md-3 col-6">
                <div class="change_profile_img mx-auto mb-4">
                    <div class="avatar-preview">
                        <?php if($user_list->image): ?>
                            <div id="imagePreview" style="background-image: url(<?php echo e(generateSignedUrl($user_list->image)); ?>);"></div>
                        <?php else: ?>
                            <div id="imagePreview" style="background-image: url(<?php echo e(default_user_placeholder()); ?>)"></div>
                        <?php endif; ?>
                    </div>
                </div>

                
            </div>

            <div class="col-lg-10 col-md-9">
                <div class="row">
                    <div class="col-lg-12 col-md-9">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="card">
                                    <div class="card-header border-bottom"><h5 class="mb-0">Personal Details</h5></div>
                                    <input type="hidden" value="<?php echo e($user_id); ?>" id="useridc" name="useridc">
                                    <div class="card-body p-0">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">School Name :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->school_name); ?></h6>
                                            </li>

                                            

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Grade Level :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php if($user_list->grade_levels_id): ?> <?php echo e(rtrim(gradeLevel($user_list->grade_levels_id), ",")); ?> <?php endif; ?> </h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Organization Type :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->organization_type); ?></h6>
                                            </li>

                                            

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Website :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->website_url); ?></h6>
                                            </li>

                                            <?php if(isset($user_list->danme)): ?>
                                                <li class="list-group-item d-flex">
                                                    <span class="col-lg-4 col-md-3 col-4 pl-0">District :</span>
                                                    <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->danme); ?></h6>
                                                </li>
                                            <?php endif; ?>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Email :</span>
                                                <a class="col-lg-8 col-md-9 col-8 pr-0 mb-0" href="mailto:<?php echo e($user_list->email); ?>"><?php echo e($user_list->email); ?></a>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Address :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->address); ?> </h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">State :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->state); ?> </h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">City :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->city); ?></h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Zipcode :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->zipcode); ?></h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Note :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0"><?php echo e($user_list->about); ?></h6>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- EDIT PROFILE SECTION END -->

        
        <ul class="nav nav-tabs" id="actionTabs" role="tablist">
            <li class="nav-item"><a class="nav-link active" id="user-action-tab" data-toggle="tab" href="#user-action" role="tab" aria-controls="user-action" aria-selected="true">User Action</a></li>
            <li class="nav-item"><a class="nav-link" id="user-requirements-tab" data-toggle="tab" href="#user-requirements" role="tab" aria-controls="user-requirements" aria-selected="false">Requirements</a></li>
            <li class="nav-item"><a class="nav-link" id="user-programs-tab" data-toggle="tab" href="#user-programs" role="tab" aria-controls="user-programs" aria-selected="false">Programs</a></li>
        </ul>
        <div class="tab-content mt-3" id="actionTabsContent">
            <div class="tab-pane fade show active" id="user-action" role="tabpanel" aria-labelledby="user-action-tab">
                <?php echo $__env->make('admin.marketplace.schools.tabs.user-action', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <div class="tab-pane fade" id="user-requirements" role="tabpanel" aria-labelledby="user-requirements-tab">
                <?php echo $__env->make('admin.marketplace.schools.tabs.user-requirements', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <div class="tab-pane fade" id="user-programs" role="tabpanel" aria-labelledby="user-programs-tab">
                <?php echo $__env->make('admin.marketplace.schools.tabs.user-programs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        </div>
        

    </div>
</main>
<!-- MAIN SECTION END -->
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/schools/viewplatformschools.blade.php ENDPATH**/ ?>