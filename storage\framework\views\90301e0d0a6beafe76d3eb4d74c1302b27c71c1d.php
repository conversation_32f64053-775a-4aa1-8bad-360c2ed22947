<?php if(request()->segment(5) == 'locations'): ?>
    <div class="card-body p-2 px-3">
        <ul class="list-group list-group-flush">
            <?php if($educator && $educator->locations->isNotEmpty()): ?>
                <?php $__currentLoopData = $educator->locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Location:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($location->location ?? '-'); ?></h6>

                        <span class="col-lg-3 col-md-3 col-4 pl-0">Address:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($location->address ?? '-'); ?></h6>
                    </li>

                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Latitude:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($location->lat ?? '-'); ?></h6>

                        <span class="col-lg-3 col-md-3 col-4 pl-0">Longitude:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($location->lng ?? '-'); ?></h6>
                    </li>

                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Radius:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($location->radius ?? '-'); ?> Miles</h6>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <li class="list-group-item">No data found</li>
            <?php endif; ?>
        </ul>
    </div>
<?php endif; ?>
<?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/educator/overview-steps/locations.blade.php ENDPATH**/ ?>