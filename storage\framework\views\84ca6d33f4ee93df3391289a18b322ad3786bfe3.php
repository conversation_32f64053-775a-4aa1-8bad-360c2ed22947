<?php if(request()->segment(4) == 'step3'): ?>
<div class="card-body p-2 px-3">
    <ul class="list-group list-group-flush">
        <?php    
            $tools = '';
        ?>

        <?php if($data['user_third_step']->isNotEmpty()): ?>
        <?php $__currentLoopData = $data['user_third_step']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php
            $tools = $step->tools;
        ?>
        <h5>Certificates and Licenses</h5>
        <li class="list-group-item d-flex border-0">
            <span class="col-lg-3 col-md-3 col-4 pl-0">I am a certified
                teacher:</span>
            <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                <?php if(!empty($step->certification)): ?>
                <?php echo e(ucfirst($step->certification)); ?>

                <?php endif; ?>
            </h6>


            <span class="col-lg-3 col-md-3 col-4 pl-0">Profile type:</span>
            <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                <?php if(!empty($step->profile_type)): ?>
                <?php if($step->profile_type == 'certificate'): ?>
                Certified
                Teacher
                <?php else: ?>
                <?php echo e(ucfirst($step->profile_type)); ?>

                <?php endif; ?>
                <?php endif; ?>

                <?php if(!empty($step->profile_type)): ?>
                <?php if($step->certification == 'no'): ?>
                <?php if($step->profile_type == 'Other'): ?>
                (<?php echo e($step->specify); ?>)
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>
            </h6>
        </li>
        <?php if($step->certification == 'yes'): ?>
        <?php $__currentLoopData = $step->education; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $educations): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="list-group-item d-flex p-0 border-0">
            <ul class="list-group list-group-flush w-100 mx-2 my-3 border border-light rounded">
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Credentialing Agency:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php if(!empty($educations->credentialing_agency)): ?>
                        <?php echo e($educations->credentialing_agency); ?>

                        <?php endif; ?>
                    </h6>
                    <?php if(!empty($educations->credentialing_agency) && !empty($educations->credentialing_agency_other)): ?>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Other Credentialing Agency:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e($educations->credentialing_agency_other); ?>

                    </h6>
                    <?php endif; ?>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Certificates and Licenses:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php if(!empty($educations->education)): ?>
                        <?php echo e($educations->education); ?>

                        <?php endif; ?>
                    </h6>
                    <?php if(!empty($educations->education) && !empty($educations->certifications_other)): ?>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Other Certificates and Licenses :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e($educations->certifications_other); ?>

                    </h6>
                    <?php endif; ?>
                </li>

                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Teaching
                        certification valid till (Year):</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php if(!empty($educations->certification_year)): ?>
                        <?php echo e($educations->certification_year); ?>

                        <?php endif; ?>
                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">States:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php if(!empty($educations->states)): ?>
                        <?php echo e(implode(', ', array_map(fn($state) => str_replace('_', ' ', $state), json_decode($educations->states, true)))); ?>

                        <?php endif; ?>
                    </h6>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Certificate:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php if(!empty($educations->certificate)): ?>
                        <a target="_blank" href="<?php echo e(generateSignedUrl($educations->certificate)); ?>">
                            <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                        </a>
                        <?php endif; ?>
                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Document visible to the
                        schools
                        :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e((!empty($educations->certification_visible_to_school) && $educations->certification_visible_to_school == 1) ? 'Yes' : 'No'); ?>

                    </h6>
                </li>
            </ul>
        </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


        <?php endif; ?>
        
        <div>

            <h5>Additional Certifictes</h5>
            <?php if($data['additional_certificates']->isEmpty()): ?>
            <p>No Data found.</p>
            <?php endif; ?>
            <?php if($data['additional_certificates']->isNotEmpty()): ?>
            <?php $__currentLoopData = $data['additional_certificates']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $additional_certificate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

           
            <ul class="list-group list-group-flush w-100 mx-2 my-3 border border-light rounded">
                 <!-- //this is for the category side -->
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Category:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">

                        <?php if(!is_numeric($additional_certificate->category_id)): ?>
                       Other
                        <?php elseif(is_numeric($additional_certificate->category_id)): ?>
                        <?php echo e(additional_category_name($additional_certificate->category_id)); ?>

                        <?php endif; ?>

                    </h6>
                    <?php if(!is_numeric($additional_certificate->category_id)): ?>
                    <span class="pr-2">Other Category:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                     <?php echo e($additional_certificate->category_id); ?>

                    </h6>
                    <?php endif; ?>


                </li>

                <!-- //this is for the subcategory side -->
                <li class="list-group-item d-flex">

                    <span class="col-lg-3 col-md-3 col-4 pl-0">Subcategory:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php if(!is_numeric($additional_certificate->sub_category_name)): ?>
                        other
                        <?php else: ?>
                        <?php echo e(additional_subcategory_name($additional_certificate->sub_category_name)); ?>

                        <?php endif; ?>

                    </h6>
                    <?php if(!is_numeric($additional_certificate->sub_category_name)): ?>
                    <span class="pr-2">Other Subcategory:</span>
                    <h6>
                    <?php echo e($additional_certificate->sub_category_name); ?>


                    </h6>
                    <?php endif; ?>

                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Issue Date:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e($additional_certificate->issued_date); ?>

                    </h6>

                    <span class="col-lg-3 col-md-3 col-4 pl-0">Valid Till Date:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e($additional_certificate->valid_till_date=="none"?"":$additional_certificate->valid_till_date); ?>

                    </h6>

                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Visible to School:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e($additional_certificate->visible_to_school == 1 ? 'Yes' : 'No'); ?>

                    </h6>

                    <span class="col-lg-3 col-md-3 col-4 pl-0">Do not Expire:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e($additional_certificate->do_not_expire == 1 ? 'Yes' : 'No'); ?>

                    </h6>

                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Certificate:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">

                        <a target="_blank" href="<?php echo e(generateSignedUrl($additional_certificate->certificate)); ?>">
                            <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                        </a>


                </li>
            </ul>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>


        </div>

        
        <h5>Additional tools</h5>
        <?php if(!empty($tools)): ?>
            <li class="list-group-item d-flex p-0 border-0">
                <ul class="list-group list-group-flush w-100 mx-2 my-3 border border-light rounded">
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Tools:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                            <?php echo e(str_replace(',', ', ', $tools)); ?>

                        </h6>
                    </li>
                </ul>
            </li>
        <?php else: ?>
            <li class="list-group-item d-flex">No data found</li>
        <?php endif; ?>

        <h5>Education</h5>
        <?php
        $level_education = !empty($step->highest_level_of_education) ? explode(',', $step->highest_level_of_education) : '';
        $other_education = !empty($step->other_education) ? explode(',', $step->other_education) : '';
        $school_college_name = !empty($step->school_college_name) ? explode(',', $step->school_college_name) : '';
        $school_location = !empty($step->school_location) ? explode(';', $step->school_location) : '';
        $month_and_year_graduation = !empty($step->month_and_year_graduation) ? explode(',', $step->month_and_year_graduation) : '';
        $gpa = !empty($step->GPA) ? explode(',', $step->GPA) : '';
        $major = !empty($step->major) ? explode(',', $step->major) : '';
        $minor = !empty($step->minor) ? explode(',', $step->minor) : '';
        $transcript = !empty($step->transcript) ? explode(',', $step->transcript) : '';
        $transcript_visible_to_school = !empty($step->transcript_visible_to_school) && $step->transcript_visible_to_school != '' ? explode(',', $step->transcript_visible_to_school) : '';
        ?>
        <?php if(!empty($level_education) && count($level_education) > 0): ?>
        <?php $__currentLoopData = $level_education; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $education): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="list-group-item d-flex p-0 border-0">
            <ul class="list-group list-group-flush w-100 mx-2 my-3 border border-light rounded">
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">School/College Name:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($school_college_name) ? $school_college_name[$index] : ''); ?>

                    </h6>

                    <span class="col-lg-3 col-md-3 col-4 pl-0">Location:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($education) ? $school_location[$index] : ''); ?>

                    </h6>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Level of Education:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($education) ? $education : ''); ?>

                    </h6>
                    <?php if(!empty($other_education[$index])): ?>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Other Degree:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e($other_education[$index]); ?>

                    </h6>
                    <?php endif; ?>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Month and Year
                        of Graduation:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($month_and_year_graduation) ? $month_and_year_graduation[$index] : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">GPA:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($gpa) ? $gpa[$index] : ''); ?>

                    </h6>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Major:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($major) ? $major[$index] : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Minor:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($minor) ? $minor[$index] : ''); ?>

                    </h6>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Transcript:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php if(!empty($transcript) && !empty($transcript[$index])): ?>
                        <a
                            target="_blank" href="<?php if(isset($transcript[$index])): ?> <?php echo e(generateSignedUrl($transcript[$index])); ?> <?php endif; ?>">
                            <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                        </a>
                        <?php endif; ?>
                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Document visible to the school :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php if(!empty($transcript_visible_to_school) && !empty($transcript_visible_to_school[$index])): ?>
                        <?php if($transcript_visible_to_school == ''): ?>
                        No
                        <?php elseif($transcript_visible_to_school[$index] == 0): ?>
                        No
                        <?php else: ?>
                        Yes
                        <?php endif; ?>
                        <?php else: ?>
                        No
                        <?php endif; ?>
                    </h6>
                </li>
            </ul>
        </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
        <li class="list-group-item d-flex">No data found</li>
        <?php endif; ?>


        <h5>Experience</h5>
        <li class="list-group-item d-flex border-0">
            <span class="col-lg-3 col-md-3 col-4 pl-0">Total Exp:</span>
            <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                <?php echo e(!empty($step->total_experience) ? $step->total_experience : ''); ?>

            </h6>
        </li>
        <?php if(count($step->teching) > 0): ?>
        <?php $__currentLoopData = $step->teching; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teching): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="list-group-item d-flex p-0 border-0">
            <ul class="list-group list-group-flush w-100 mx-2 my-3 border border-light rounded">
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Employer Name:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->employer_name) ? $teching->employer_name : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Location:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->location) ? $teching->location : ''); ?>

                    </h6>
                </li>

                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Position:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->position) ? $teching->position : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Start Date:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->start_date) ? $teching->start_date : ''); ?>

                    </h6>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">End Date:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->end_date) ? $teching->end_date : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Currently working here</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e((!empty($teching->currently_working_here) && $teching->currently_working_here == 1) ? 'Yes' : 'No'); ?>

                    </h6>
                </li>
            </ul>
        </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
        <li class="list-group-item d-flex">No data found</li>
        <?php endif; ?>

        <h5>References</h5>
        <?php if(count($step->references) > 0): ?>
        <?php $__currentLoopData = $step->references; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reference): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="list-group-item d-flex p-0 border-0">
            <ul class="list-group list-group-flush w-100 mx-2 my-3 border border-light rounded">
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Full Name :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($reference->full_name) ? $reference->full_name : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Email:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($reference->email) ? $reference->email : ''); ?>

                    </h6>
                </li>

                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Phone:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($reference->phone) ? $reference->phone : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Make this visible to schools</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e((!empty($reference->reference_visible) && $reference->reference_visible == 1) ? 'Yes' : 'No'); ?>

                    </h6>
                </li>
            </ul>
        </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
        <li class="list-group-item d-flex">No data found</li>
        <?php endif; ?>

        <h5>Other Work Experience</h5>
        <?php if(count($step->otherExper) > 0): ?>
        <?php $__currentLoopData = $step->otherExper; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teching): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="list-group-item d-flex p-0 border-0">
            <ul class="list-group list-group-flush w-100 mx-2 my-3 border border-light rounded">
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Employer Name:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->employer_name) ? $teching->employer_name : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Location:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->location) ? $teching->location : ''); ?>

                    </h6>
                </li>

                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Position:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->position) ? $teching->position : ''); ?>

                    </h6>
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Start Date:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->start_date) ? $teching->start_date : ''); ?>

                    </h6>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">End Date:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e(!empty($teching->end_date) ? $teching->end_date : ''); ?>

                    </h6>

                    <span class="col-lg-3 col-md-3 col-4 pl-0">Currently working here</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                        <?php echo e((!empty($teching->currently_working_here) && $teching->currently_working_here == 1) ? 'Yes' : 'No'); ?>

                    </h6>
                </li>
            </ul>
        </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
        <li class="list-group-item d-flex">No data found</li>
        <?php endif; ?>
        <h5>Resume</h5>
        <?php if(!empty($step->resume)): ?>
        <li class="list-group-item d-flex">
            <span class="col-lg-3 col-md-3 col-4 pl-0">Resume:</span>
            <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                <a target="_blank" href="<?php echo e(generateSignedUrl($step->resume)); ?>">
                    <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                </a>
            </h6>
        </li>
        <?php else: ?>
        <li class="list-group-item d-flex">No data found</li>
        <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
        <li class="list-group-item d-flex">No data found</li>
        <?php endif; ?>
    </ul>

    

</div>
<?php endif; ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/application-steps/step-3.blade.php ENDPATH**/ ?>