<?php if(request()->segment(4) == 'step6'): ?>
    <div class="card-body p-2 px-3">
        <?php if(!empty($data['quiz'])): ?>
            <h5>Quiz</h5>
            <?php $i=0; ?>
            <?php if(!empty($data['question'])): ?>
                <?php $__currentLoopData = $data['question']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $rowsquestion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php $i++; ?>
                    <?php if(!empty($data['quiz'])): ?>
                        <?php $quest=(array)json_decode($data['quiz']->quiz_question_answer,true); ?>
                    <?php endif; ?>

                    <div class="login__form form-title que mt-3">
                        <p><?php echo e($i); ?>.<?php echo e($rowsquestion->questions); ?></p>
                        <input type="hidden" name="question<?php echo e($key); ?>" value="<?php echo e($rowsquestion->question_id); ?>">

                        <?php if(!empty($rowsquestion->option_one)): ?>
                            <div class="check-box">
                                <div class="form-check <?php if($rowsquestion->answer == 1): ?> right-answer <?php endif; ?> " style="pointer-events: none;">
                                    <input class="form-check-input pe-none " type="radio"
                                        name="option<?php echo e($key); ?>" id="option<?php echo e($key); ?>_1" value="1"
                                        <?php if(!empty($data['quiz'])): ?> <?php if(isset($quest[$i])): ?> <?php if($quest[$i] == '1'): ?> <?php echo e('checked'); ?> <?php endif; ?>
                                        <?php endif; ?> <?php endif; ?>>
                                    <label class="form-check-label" for="option<?php echo e($key); ?>_1">
                                        <?php echo e($rowsquestion->option_one); ?>

                                    </label>
                                </div>
                            </div>
                        <?php endif; ?>
                        <?php if(!empty($rowsquestion->option_two)): ?>
                            <div class="form-check <?php if($rowsquestion->answer == 2): ?> right-answer <?php endif; ?> " style="pointer-events: none;">
                                <input class="form-check-input pe-none" type="radio" name="option<?php echo e($key); ?>"
                                    id="option<?php echo e($key); ?>_2" value="2"
                                    <?php if(!empty($data['quiz'])): ?> <?php if(isset($quest[$i])): ?> <?php if($quest[$i] == '2'): ?> <?php echo e('checked'); ?> <?php endif; ?>
                                    <?php endif; ?> <?php endif; ?>>
                                <label class="form-check-label" for="option<?php echo e($key); ?>_2">
                                    <?php echo e($rowsquestion->option_two); ?>

                                </label>
                            </div>
                        <?php endif; ?>
                        <?php if(!empty($rowsquestion->option_three)): ?>
                            <div class="form-check <?php if($rowsquestion->answer == 3): ?> right-answer <?php endif; ?> " style="pointer-events: none;">
                                <input class="form-check-input pe-none" type="radio" name="option<?php echo e($key); ?>"
                                    id="option<?php echo e($key); ?>_3" value="3"
                                    <?php if(!empty($data['quiz'])): ?> <?php if(isset($quest[$i])): ?> <?php if($quest[$i] == '3'): ?> <?php echo e('checked'); ?> <?php endif; ?>
                                    <?php endif; ?> <?php endif; ?>>
                                <label class="form-check-label" for="option<?php echo e($key); ?>_3">
                                    <?php echo e($rowsquestion->option_three); ?>

                                </label>
                            </div>
                        <?php endif; ?>
                        <?php if(!empty($rowsquestion->option_four)): ?>
                            <div class="form-check <?php if($rowsquestion->answer == 4): ?> right-answer <?php endif; ?> " style="pointer-events: none;">
                                <input class="form-check-input pe-none" type="radio" name="option<?php echo e($key); ?>"
                                    id="option<?php echo e($key); ?>_4" value="4"
                                    <?php if(!empty($data['quiz'])): ?> <?php if(isset($quest[$i])): ?> <?php if($quest[$i] == '4'): ?> <?php echo e('checked'); ?> <?php endif; ?>
                                    <?php endif; ?> <?php endif; ?>>
                                <label class="form-check-label" for="option<?php echo e($key); ?>_4">
                                    <?php echo e($rowsquestion->option_four); ?>

                                </label>
                            </div>
                        <?php endif; ?>
                    </div>
                    <span id="option<?php echo e($key); ?>_error" class="error rederror"></span>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php endif; ?>

<?php if(request()->segment(4) == 'step6'): ?>
    <div class="card-body p-2 px-3">
        <?php $i=0; ?>
        <?php if(!empty($data['assessment'])): ?>
            <h5>Free Assissment</h5>
            <?php $__currentLoopData = $data['assessment']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $rowsquestion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php $i++; ?>
                <?php if(!empty($data['assessments_ans'])): ?>
                    <?php $quest=(array)json_decode($data["assessments_ans"]->assessment_answer,true);?>
                <?php endif; ?>

                <div class="login__form form-title assment_que mb-4">
                    <p><?php echo e($i); ?>. <?php echo e($rowsquestion->questions); ?>*</p>
                    <input type="hidden" name="assessment_question<?php echo e($key); ?>"
                        value="<?php echo e($rowsquestion->id); ?>">
                    <div class="text-box">
                        <textarea class="ans-text" name="answer<?php echo e($key); ?>" id="answer-<?php echo e($key); ?>" cols="140" rows="5" maxlength="500" disabled><?php if(!empty($quest[$i])): ?> <?php echo e($quest[$i]); ?> <?php endif; ?></textarea>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/application-steps/step-6.blade.php ENDPATH**/ ?>