<div class="table-responsive">
    <table class="table table-striped table-bordered mb-0" id="<?php echo e($tableId ?? ''); ?>">
        <thead class="thead-dark">
            <tr>
                <th>#</th>
                <th>Educator Name</th>
                <th>State</th>
                <th>Subject Area</th>
                <th>Subject Name</th>
                <th>Grade Levels</th>
                <th>Certified States</th>
                <th>Certification</th>
                <?php if(!isset($showShortlist) || $showShortlist): ?>
                    <th>Shortlist</th>
                <?php endif; ?>
                <th>Shortlisted By</th>
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = ($rows ?? []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($index + 1); ?></td>
                    <td>
                        <?php echo e($r['name'] ?? '-'); ?>

                        <span class="shortlist-meta d-none" data-user-id="<?php echo e($r['user_id'] ?? 0); ?>" data-requirement-id="<?php echo e($r['requirement_id'] ?? 0); ?>"></span>
                    </td>
                    <td><?php echo e($r['state'] ?? '-'); ?></td>
                    <td><?php echo e($r['subject_area'] ?? '-'); ?></td>
                    <td><?php echo e($r['subject_name'] ?? '-'); ?></td>
                    <td><?php echo e($r['grade_levels'] ?? '-'); ?></td>
                    <td><?php echo e($r['certified_states'] ?? '-'); ?></td>
                    <td>
                        <?php if(!empty($r['has_certification'])): ?>
                            <span class="text-success"><i class="fa fa-check"></i></span>
                        <?php else: ?>
                            <span class="text-muted">-</span>
                        <?php endif; ?>
                    </td>
                    <?php if(!isset($showShortlist) || $showShortlist): ?>
                        <td>
                            <?php
                                $liked = isset($r['shortlist_status']) && (int) $r['shortlist_status'] === 1;
                                $disliked = isset($r['shortlist_status']) && (int) $r['shortlist_status'] === 0;
                            ?>
                            <a href="javascript:void(0)" class="mx-1 shortlist-like" data-user-id="<?php echo e($r['user_id'] ?? 0); ?>" data-requirement-id="<?php echo e($r['requirement_id'] ?? 0); ?>" data-status="1" title="Like">
                                <i class="fa fa-thumbs-up <?php echo e($liked ? 'text-success' : 'text-muted'); ?>"></i>
                            </a>
                            <a href="javascript:void(0)" class="mx-1 shortlist-dislike" data-user-id="<?php echo e($r['user_id'] ?? 0); ?>" data-requirement-id="<?php echo e($r['requirement_id'] ?? 0); ?>" data-status="0" title="Dislike">
                                <i class="fa fa-thumbs-down <?php echo e($disliked ? 'text-danger' : 'text-muted'); ?>"></i>
                            </a>
                        </td>
                    <?php endif; ?>
                    <td><?php echo e($r['updated_by'] ?? '-'); ?></td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="10" class="text-center text-muted">No data</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/requirements/tabs/proposal_table.blade.php ENDPATH**/ ?>