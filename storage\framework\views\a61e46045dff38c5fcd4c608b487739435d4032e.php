<!-- Invite <PERSON> -->
<style>
    .truncate-text {
        display: inline-block;
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
        cursor: pointer;
    }
</style>
<div class="d-flex justify-content-end gap-2 align-items-center mb-3">
    <button type="button" class="mx-3 btn btn-primary"
        data-toggle="modal"
        data-target="#mainInstructorInvitePopup"
        id="send-invite"
        onclick="inviteMainInstructor('<?php echo e(route('admin.marketplace-requirementsInvite')); ?>', '<?php echo e(encrypt_str($data->id)); ?>')">
        Main Instructor Invite
    </button>
    <button type="button" class="btn btn-primary"
        data-toggle="modal"
        data-target="#standByInvitePopup"
        id="send-invite"
        onclick="inviteStandbyInstructor('<?php echo e(route('admin.marketplace-requirementsInvite')); ?>', '<?php echo e(encrypt_str($data->id)); ?>')">
        Standby Instructor Invite
    </button>
</div>


<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="tab-all" data-toggle="tab" href="#pane-all" role="tab" aria-controls="pane-all" aria-selected="true">All</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="tab-shortlisted" data-toggle="tab" href="#pane-shortlisted" role="tab" aria-controls="pane-shortlisted" aria-selected="false">Shortlisted</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="tab-message" data-toggle="tab" href="#pane-message" role="tab" aria-controls="pane-message" aria-selected="false">Message</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="tab-archived" data-toggle="tab" href="#pane-archived" role="tab" aria-controls="pane-archived" aria-selected="false">Archived</a>
            </li>
        </ul>
    </div>
    <div class="card-body p-0">
        <div class="tab-content">
            <div class="tab-pane fade show active" id="pane-all" role="tabpanel" aria-labelledby="tab-all">
                <?php echo $__env->make('admin.marketplace.requirements.tabs.proposal_table', ['rows' => $allRows, 'tableId' => 'proposal-table-all', 'showShortlist' => true], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <div class="tab-pane fade" id="pane-shortlisted" role="tabpanel" aria-labelledby="tab-shortlisted">
                <?php echo $__env->make('admin.marketplace.requirements.tabs.proposal_table', ['rows' => $shortlistedRows, 'tableId' => 'proposal-table-shortlisted', 'showShortlist' => false], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <div class="tab-pane fade" id="pane-message" role="tabpanel" aria-labelledby="tab-message">
                <p class="text-muted m-3">No data.</p>
            </div>
            <div class="tab-pane fade" id="pane-archived" role="tabpanel" aria-labelledby="tab-archived">
                <?php echo $__env->make('admin.marketplace.requirements.tabs.proposal_table', ['rows' => $archivedRows, 'tableId' => 'proposal-table-archived', 'showShortlist' => false], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
        </div>
    </div>
</div>


<!-- Modal (only for sending invites) -->
<div class="modal" id="mainInstructorInvitePopup" tabindex="-1" aria-labelledby="mainInstructorInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>

<!-- Modal (only for sending invites) -->
<div class="modal" id="standByInvitePopup" tabindex="-1" aria-labelledby="standByInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>


<!-- Scripts will be moved to parent file --><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/requirements/tabs/proposal.blade.php ENDPATH**/ ?>