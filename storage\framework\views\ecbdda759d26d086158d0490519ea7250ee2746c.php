<div class="card">
    <div class="card-header">Contracts</div>
    <div class="card-body p-0">
        <?php if(isset($contractVersions) && count($contractVersions) > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th>#</th>
                            <th>Legal First Name</th>
                            <th>Legal Last Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Version Number</th>
                            <th>Document</th>
                            <th>Notes</th>
                            <th>Created At</th>
                            <th>Created By</th>
                            <th>Updated At</th>
                            <th>Updated By</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $contractVersions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $version): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($version['id']); ?></td>
                                <td><?php echo e($version['legal_first_name'] ?? 'N/A'); ?></td>
                                <td><?php echo e($version['legal_last_name'] ?? 'N/A'); ?></td>
                                <td><?php echo e($version['phone'] ?? 'N/A'); ?></td>
                                <td><?php echo e($version['email'] ?? 'N/A'); ?></td>
                                <td><?php echo e($version['version_number'] ?? 'v1.0'); ?></td>
                                <td>
                                    <?php if($version['file_url']): ?>
                                        <a href="<?php echo e(generateSignedUrl($version['file_url'])); ?>" target="_blank" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download"></i> Download
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">No file</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($version['notes'] ?? '-'); ?></td>
                                <td><?php echo e($version['created_at'] ? \Carbon\Carbon::parse($version['created_at'])->format('M d, Y H:i') : 'N/A'); ?></td>
                                <td><?php echo e($version['created_by']); ?></td>
                                <td><?php echo e($version['updated_at'] ? \Carbon\Carbon::parse($version['updated_at'])->format('M d, Y H:i') : 'N/A'); ?></td>
                                <td><?php echo e($version['updated_by']); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-file-contract fa-3x text-muted"></i>
                </div>
                <h5 class="text-muted">No Contracts Found</h5>
                <p class="text-muted">No contract documents have been uploaded for this requirement yet.</p>
            </div>
        <?php endif; ?>
    </div>
</div><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/requirements/tabs/contract.blade.php ENDPATH**/ ?>