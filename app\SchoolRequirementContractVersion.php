<?php

namespace App;

use App\Models\v1\SchoolUser;
use App\User;
use Illuminate\Database\Eloquent\Model;

class SchoolRequirementContractVersion extends Model
{
    protected $table = 'school_requirement_contract_versions';
    protected $fillable = [
        'school_requirement_contract_id',
        'file_url',
        'version_number',
        'notes',
        'created_by_id',
        'created_by_type',
        'updated_by_id',
        'updated_by_type',
    ];

    public function contract()
    {
        return $this->belongsTo(SchoolRequirementContract::class, 'school_requirement_contract_id');
    }

    public function createdBy()
    {
        return $this->morphTo('created_by');
    }

    public function updatedBy()
    {
        return $this->morphTo('updated_by');
    }
}
