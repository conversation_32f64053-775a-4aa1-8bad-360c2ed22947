<div class="card">
    <div class="card-header">Contracts</div>
    <div class="card-body p-0">
        @if(isset($contractVersions) && count($contractVersions) > 0)
            <div class="table-responsive">
                <table class="table table-striped table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th>#</th>
                            <th>Legal First Name</th>
                            <th>Legal Last Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Version Number</th>
                            <th>Document</th>
                            <th>Notes</th>
                            <th>Created At</th>
                            <th>Created By</th>
                            <th>Updated At</th>
                            <th>Updated By</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($contractVersions as $version)
                            <tr>
                                <td>{{ $version['id'] }}</td>
                                <td>{{ $version['legal_first_name'] ?? 'N/A' }}</td>
                                <td>{{ $version['legal_last_name'] ?? 'N/A' }}</td>
                                <td>{{ $version['phone'] ?? 'N/A' }}</td>
                                <td>{{ $version['email'] ?? 'N/A' }}</td>
                                <td>{{ $version['version_number'] ?? 'v1.0' }}</td>
                                <td>
                                    @if($version['file_url'])
                                        <a href="{{ generateSignedUrl($version['file_url']) }}" target="_blank" class="btn btn-sm btn-primary">
                                            <i class="fas fa-download"></i> Download
                                        </a>
                                    @else
                                        <span class="text-muted">No file</span>
                                    @endif
                                </td>
                                <td>{{ $version['notes'] ?? '-' }}</td>
                                <td>{{ $version['created_at'] ? \Carbon\Carbon::parse($version['created_at'])->format('M d, Y H:i') : 'N/A' }}</td>
                                <td>{{ $version['created_by'] }}</td>
                                <td>{{ $version['updated_at'] ? \Carbon\Carbon::parse($version['updated_at'])->format('M d, Y H:i') : 'N/A' }}</td>
                                <td>{{ $version['updated_by'] }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-file-contract fa-3x text-muted"></i>
                </div>
                <h5 class="text-muted">No Contracts Found</h5>
                <p class="text-muted">No contract documents have been uploaded for this requirement yet.</p>
            </div>
        @endif
    </div>
</div>