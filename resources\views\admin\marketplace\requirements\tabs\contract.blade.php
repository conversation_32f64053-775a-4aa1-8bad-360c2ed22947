<div class="card">
    <div class="card-header">Contracts</div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-bordered mb-0">
                <thead class="thead-dark">
                    <tr>
                        <th>#</th>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Phone</th>
                        <th>Email</th>
                        <th>Version Number</th>
                        <th>File</th>
                        <th>Created At</th>
                        <th>Created By</th>
                        <th>Updated At</th>
                        <th>Updated By</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($contracts as $contract)
                        <tr>
                            <td>{{ $contract->id }}</td>
                            <td>{{ $contract->legal_first_name }}</td>
                            <td>{{ $contract->legal_last_name }}</td>
                            <td>{{ $contract->phone }}</td>
                            <td>{{ $contract->email }}</td>
                            <td>{{ $contract->version_number }}</td>
                            <td><a href="{{ generateSignedUrl($contract->file_url) }}" target="_blank">Download</a></td>
                            <td>{{ $contract->created_at }}</td>
                            <td>{{ $contract->createdBy->first_name ?? 'N/A' }}</td>
                            <td>{{ $contract->updated_at }}</td>
                            <td>{{ $contract->updatedBy->first_name ?? 'N/A' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>