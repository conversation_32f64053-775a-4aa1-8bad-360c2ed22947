

<?php $__env->startSection('title'); ?>
    Requirements Details | Whizara
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php
    $res = get_permission(session('Adminnewlogin')['type']); ?>
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/requirements')); ?>">Requirements List</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Requirements Details</li>
                    <li class="breadcrumb-item active" aria-current="page"><a href="<?php echo e(url('admin/k12connections/add-requirements')); ?>">Add Requirement</a></li>
                </ol>
            </nav>
            

            
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        
                        <div class="card-header border-bottom">
                            <h5 class="mb-0">Requirements Details</h5>
                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-3 col-md-3 col-4 pl-0">School Name :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->school->school_name ?? ''); ?></h6>

                                    <span class="col-lg-3 col-md-3 col-4 pl-0">District School ID :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->district_school_id ?? ''); ?></h6>
                                </li>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Class Type :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e(classType($data->class_type)); ?></h6>

                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Delivery Mode :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e(ucfirst($data->delivery_mode)); ?></h6>
                                </li>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Subject Area :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e(v1SubjectAreaName($data->subject_area_id) ?? ''); ?></h6>

                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Subject :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e(v1SubjectName($data->subject_id) ?? ''); ?></h6>
                                </li>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Capacity :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->capacity); ?></h6>

                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Grade Levels :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e(rtrim(gradeLevel($data->grade_levels_id), ',')); ?></h6>
                                </li>
                                <?php if($data->requirement_tags): ?>
                                    <?php $tags = json_decode($data->requirement_tags, true); ?>
                                    <?php if(!empty($tags)): ?>
                                        <li class="list-group-item d-flex">
                                            <span class="col-lg-3 col-md-3 col-4 pl-0">Tags:</span>
                                            <h6 class="col-lg-9 col-md-9 col-8 pr-0 mb-0"><?php echo e(implode(', ', $tags)); ?></h6>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if($data->description): ?>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Description :</span>
                                        <h6 class="col-lg-9 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->description); ?></h6>
                                    </li>
                                <?php endif; ?>
                                <?php if($data->benefits): ?>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Benefits :</span>
                                        <h6 class="col-lg-9 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->benefits); ?></h6>
                                    </li>
                                <?php endif; ?>
                            </ul>
                            

                            
                            <ul class="list-group list-group-flush">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Class Details</h5>
                                </div>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Class Start Date</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e(date('m-d-Y', strtotime($data->start_date))); ?></h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Class End Date</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e(date('m-d-Y', strtotime($data->end_date))); ?></h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Time Zone</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->time_zone ?? 'Not Specified'); ?></h6>
                                </li>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Number of Instructional Days :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->no_instrtructional_days); ?></h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Class Duration :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->class_duration / 60); ?> hr</h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Number of non-instructional hours :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->no_non_instructional_hr); ?> hr</h6>
                                </li>
                            </ul>

                            <?php if($data->delivery_mode == 'in-person'): ?>
                                <ul class="list-group list-group-flush">
                                    <div class="card-header border-bottom">
                                        <h5 class="mb-0">Location</h5>
                                    </div>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Street Address :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->address); ?></h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">City :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->city); ?></h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">State :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->state); ?></h6>
                                    </li>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Country :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->country); ?></h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Lattitude :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->lat); ?></h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Longitude :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->lng); ?></h6>
                                    </li>
                                </ul>
                            <?php endif; ?>
                            <ul class="list-group list-group-flush">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Schedule</h5>
                                </div>
                                <?php if($data->schedule_type == 'regular'): ?>
                                    <?php
                                        $schedules = is_string($data->regular_days) ? json_decode($data->regular_days, true) : $data->regular_days;
                                    ?>
                                    <?php if(is_array($schedules)): ?>
                                        <?php $__currentLoopData = $schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-2 col-md-3 col-4 pl-0">Day :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($schedule['day'] ?? ''); ?></h6>

                                                <span class="col-lg-2 col-md-3 col-4 pl-0">Start Time :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($schedule['start_time'] ?? ''); ?></h6>

                                                <span class="col-lg-2 col-md-3 col-4 pl-0">End Time :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($schedule['end_time'] ?? ''); ?></h6>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php if($data->schedule_type == 'alternating'): ?>
                                    <?php
                                        $schedules = is_string($data->schedule_1_days) ? json_decode($data->schedule_1_days, true) : $data->schedule_1_days;
                                    ?>
                                    <?php if(is_array($schedules)): ?>
                                        <?php $__currentLoopData = $schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-2 col-md-3 col-4 pl-0">Day :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($schedule['day'] ?? ''); ?></h6>

                                                <span class="col-lg-2 col-md-3 col-4 pl-0">Start Time :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($schedule['start_time'] ?? ''); ?></h6>

                                                <span class="col-lg-2 col-md-3 col-4 pl-0">End Time :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($schedule['end_time'] ?? ''); ?></h6>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </ul>
                            

                            
                            <?php if($data->sch_cal_screenshot || $data->district_cal_screenshot || $data->teacher_schedule_screenshot): ?>
                                <ul class="list-group list-group-flush">
                                    <div class="card-header border-bottom">
                                        <h5 class="mb-0">Screenshots</h5>
                                    </div>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-2 col-md-3 col-4 pl-0">School Calendar :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">
                                            <a target="_blank" href="<?php echo e(generateSignedUrl($data->sch_cal_screenshot)); ?>">
                                                <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                                            </a>
                                        </h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">District Calendar :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">
                                            <a target="_blank" href="<?php echo e(generateSignedUrl($data->district_cal_screenshot)); ?>">
                                                <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                                            </a>
                                            </h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Teacher Schedule :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">
                                            <a target="_blank" href="<?php echo e(generateSignedUrl($data->teacher_schedule_screenshot)); ?>">
                                                <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                                            </a>
                                        </h6>
                                    </li>
                                </ul>
                            <?php endif; ?>
                            

                            
                            <ul class="list-group list-group-flush">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Others</h5>
                                </div>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Experience</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->experience); ?></h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Qualifications :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->qualifications); ?></h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Total Budget :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->total_budget); ?> $</h6>
                                </li>
                            </ul>
                            

                            
                            <?php if($data->proctor): ?>
                                <ul class="list-group list-group-flush">
                                    <div class="card-header border-bottom">
                                        <h5 class="mb-0">Proctor</h5>
                                    </div>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Proctor Name :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->proctor->proctor_name ?? ''); ?></h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Proctor Email :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->proctor->email ?? ''); ?></h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Proctor Phone :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0"><?php echo e($data->proctor->phone ?? ''); ?></h6>
                                    </li>
                                </ul>
                            <?php endif; ?>
                            

                            
                            <?php if($data->meetingLinks): ?>
                                <ul class="list-group list-group-flush">
                                    <div class="card-header border-bottom">
                                        <h5 class="mb-0">Meeting Links</h5>
                                    </div>
                                    <?php $__currentLoopData = $data->meetingLinks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="list-group-item d-flex">
                                            <span class="col-lg-3 col-md-3 col-4 pl-0">Link :</span>
                                            <h6 class="col-lg-9 col-md-3 col-8 pr-0 mb-0"><?php echo e($link->link); ?></h6>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            <?php endif; ?>
                            
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Nav tabs -->
            <ul class="nav nav-tabs" id="requirementTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="invites-tab" data-toggle="tab" href="#invites" role="tab" aria-controls="invites" aria-selected="true">
                    Invites
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="proposals-tab" data-toggle="tab" href="#proposals" role="tab" aria-controls="proposals" aria-selected="false">
                    Proposals
                    </a>
                </li>
            </ul>

            <!-- Tab panes -->
            <div class="tab-content mt-3" id="requirementTabsContent">
                <div class="tab-pane fade show active" id="invites" role="tabpanel" aria-labelledby="invites-tab">
                    <?php echo $__env->make('admin.marketplace.requirements.tabs.invitation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="tab-pane fade" id="proposals" role="tabpanel" aria-labelledby="proposals-tab">
                    <?php echo $__env->make('admin.marketplace.requirements.tabs.proposal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            </div>

        </div>
    </main>

    <!-- MAIN SECTION END -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    async function inviteMainInstructor(url, encryptedId) {
        $('#mainInstructorInvitePopup .modal-content').html('<span class="loading p-5">Loading...</span>');
        $('#standByInvitePopup .modal-content').html('<span class="loading p-5">Loading...</span>');
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "GET",
                data: {
                    id: encryptedId,
                    invitation_type: 'main_instructor'
                }
            });

            if (response) {
                $('#mainInstructorInvitePopup .modal-content').html(response);
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }

    async function inviteStandbyInstructor(url, encryptedId) {
        $('#mainInstructorInvitePopup .modal-content').html('<span class="loading p-5">Loading...</span>');
        $('#standByInvitePopup .modal-content').html('<span class="loading p-5">Loading...</span>');
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "GET",
                data: {
                    id: encryptedId,
                    invitation_type: 'stand_by'
                }
            });

            if (response) {
                $('#standByInvitePopup .modal-content').html(response);
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }

    window.updateStatus = async function (url, id, status) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: status
                }
            });
            if (response.success) {
                alertify.success(response.message);
            } else {
                alertify.error(response.message || "Something went wrong");
            }
        } catch (error) {
            if (error.responseJSON && error.responseJSON.errors) {
                const firstError = Object.values(error.responseJSON.errors)[0][0];
                alertify.error(firstError);
            } else if (error.responseJSON && error.responseJSON.message) {
                alertify.error(error.responseJSON.message);
            } else {
                alertify.error("An unexpected error occurred");
            }
        }
    };

    async function withdrawInvite(url, id) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: 'withdraw'
                }
            });

            if (response.success) {
                alertify.success(response.message);
                $(`[data-inviteId="${id}"][data-field="status"]`).html('<span class="badge badge-dark">Withdrawn</span>');
                // Disable the withdraw button
                $(`button[onclick*="${id}"]`).prop('disabled', true);
            } else {
                alertify.error(response.message || "Something went wrong");
            }
        } catch (error) {
            if (error.responseJSON && error.responseJSON.message) {
                alertify.error(error.responseJSON.message);
            } else {
                alertify.error("An unexpected error occurred");
            }
        }
    }
    // Like/Dislike shortlist toggle
    $(document).on('click', '.shortlist-like, .shortlist-dislike', async function() {
        const el = $(this);
        const userId = el.data('user-id');
        const requirementId = el.data('requirement-id');
        const status = parseInt(el.data('status'), 10); // normalize to int (1 = like, 0 = dislike)

        // Prevent rapid double clicks
        if (el.data('loading')) return;

        // Helper: get shortlist status (1=liked, 0=disliked, null=none)
        const getStatus = (row) => {
            if (!row.length) return null;
            if (row.find('i.fa-thumbs-up').hasClass('text-success')) return 1;
            if (row.find('i.fa-thumbs-down').hasClass('text-danger')) return 0;
            return null;
        };

        // Check current status (prefer All tab row if available)
        const allRow = $(`#proposal-table-all tbody tr`).filter(function () {
            return $(this).find(`[data-user-id="${userId}"]`).length > 0;
        }).first();
        const currentStatus = allRow.length ? getStatus(allRow) : getStatus(el.closest('tr'));

        // Exit early if already in same state
        if (status === currentStatus) {
            alertify.warning(`Already ${status === 1 ? 'liked' : 'disliked'}`);
            return;
        }

        el.data('loading', true);
        try {
            const response = await $.ajax({
                headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
                url: '<?php echo e(url('admin/k12connections/requirements/shortlist-applicant')); ?>',
                method: 'POST',
                data: { user_id: userId, requirement_id: requirementId, status }
            });

            if (response?.message) alertify.success(response.message);

            // Update icons in All tab if present
            if (allRow.length) {
                allRow.find('i.fa-thumbs-up').toggleClass('text-success', status === 1).toggleClass('text-muted', status !== 1);
                allRow.find('i.fa-thumbs-down').toggleClass('text-danger', status === 0).toggleClass('text-muted', status !== 0);
            }

            // Determine target tab
            const isLike = status === 1;
            const destId = isLike ? '#proposal-table-shortlisted' : '#proposal-table-archived';
            const otherId = isLike ? '#proposal-table-archived' : '#proposal-table-shortlisted';

            // Remove user from both tabs (avoid duplicates)
            [destId, otherId].forEach(id => {
                $(`${id} tbody tr`).filter(function () {
                    return $(this).find(`[data-user-id="${userId}"]`).length > 0;
                }).remove();
            });

            // Clone row (prefer All tab row for consistency)
            const sourceRow = allRow.length ? allRow : el.closest('tr');
            const cloned = sourceRow.clone(true, true);

            // Remove shortlist buttons in non-All tabs
            if (destId !== '#proposal-table-all') {
                cloned.find('.shortlist-like, .shortlist-dislike').closest('td').remove();
            }

            // Update cloned row icons
            cloned.find('i.fa-thumbs-up').toggleClass('text-success', isLike).toggleClass('text-muted', !isLike);
            cloned.find('i.fa-thumbs-down').toggleClass('text-danger', !isLike).toggleClass('text-muted', isLike);

            // Insert updated row
            $(`${destId} tbody`).prepend(cloned);

            // Remove original row if action came from shortlist/archive
            if (el.closest('table').attr('id') !== 'proposal-table-all') {
                el.closest('tr').remove();
            }
        } catch (error) {
            const msg = error.responseJSON?.message || 'Failed to update shortlist';
            alertify.error(msg);
        } finally {
            el.data('loading', false);
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/requirements/requirements-details.blade.php ENDPATH**/ ?>