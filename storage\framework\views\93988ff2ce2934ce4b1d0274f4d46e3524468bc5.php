
<div class="card">
    <div class="card-header">User Requirements</div>
    <div class="card-body p-0">
        <?php if($processedRequirements->count() > 0): ?>
        <div class="table-responsive">
            <table class="table table-striped admin-dataTable table-bordered mb-0">
                <thead class="thead-dark">
                    <tr>
                        <th scope="col" style="text-align: left;">ID</th>
                        <th scope="col" style="text-align: left;">Delivery Mode</th>
                        <th scope="col" style="text-align: left;">Grade Levels</th>
                        <th scope="col" style="text-align: left;">Subject Area</th>
                        <th scope="col" style="text-align: left;">Subject</th>
                        <th scope="col" style="text-align: left;">Class Type</th>
                        <th scope="col" style="text-align: left;">Invites</th>
                        <th scope="col" style="text-align: left;">Potential Applicants</th>
                        <th scope="col" style="text-align: left;">SPED</th>
                        <th scope="col" style="text-align: left;">ESOL</th>
                        <th scope="col" style="text-align: left;">Curriculum</th>
                        <th scope="col" style="text-align: left;">Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $processedRequirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><a href="<?php echo e(route('admin.marketplace-viewRequirements', ['id' => $requirement['encrypted_id']])); ?>"><?php echo e($requirement['id']); ?></a></td>
                            <td>
                                <span class="badge badge-<?php echo e($requirement['delivery_mode'] == 'online' ? 'primary' : 'success'); ?>">
                                    <?php echo e(ucfirst($requirement['delivery_mode'])); ?>

                                </span>
                            </td>
                            <td><?php echo e($requirement['grade_levels']); ?></td>
                            <td><?php echo e($requirement['subject_area']); ?></td>
                            <td><?php echo e($requirement['subject']); ?></td>
                            <td><?php echo e($requirement['class_type']); ?></td>
                            <td class="text-center"><?php echo e($requirement['total_invites']); ?> <?php if($requirement['accepted_invites'] > 0): ?>(<?php echo e($requirement['accepted_invites']); ?>)<?php endif; ?> </td>
                            <td class="text-center"><?php echo e($requirement['potential_applicants']); ?></td>
                            <td class="text-center">
                                <?php if($requirement['special_education_required']): ?>
                                    <i class="fas fa-check-circle text-success" title="Special Education Required"></i>
                                <?php else: ?>
                                    <i class="fas fa-times-circle text-muted" title="Special Education Not Required"></i>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <?php if($requirement['esol_required']): ?>
                                    <i class="fas fa-check-circle text-success" title="ESOL Required"></i>
                                <?php else: ?>
                                    <i class="fas fa-times-circle text-muted" title="ESOL Not Required"></i>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <?php if($requirement['curriculum_provided']): ?>
                                    <i class="fas fa-check-circle text-success" title="Curriculum Provided"></i>
                                <?php else: ?>
                                    <i class="fas fa-times-circle text-muted" title="No Curriculum Provided"></i>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge badge-<?php echo e($requirement['status'] == 'active' ? 'success' : ($requirement['status'] == 'draft' ? 'warning' : 'secondary')); ?>">
                                    <?php echo e(ucfirst($requirement['status'])); ?>

                                </span>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
            <p class="text-muted m-3">No requirements added yet.</p>
        <?php endif; ?>
    </div>
</div><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/schools/tabs/user-requirements.blade.php ENDPATH**/ ?>