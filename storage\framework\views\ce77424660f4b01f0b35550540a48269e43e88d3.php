<?php if(request()->segment(5) == 'subjects'): ?>
    <div class="card-body p-2 px-3">
        <ul class="list-group list-group-flush">
            <?php if($educator->additionalSubjects->isNotEmpty()): ?>
                <?php $__currentLoopData = $educator->additionalSubjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="list-group-item d-flex">
                        <span class="col-lg-2 col-md-2 col-4 pl-0">Subject:</span>
                        <h6 class="col-lg-7 col-md-7 col-8 pr-0 mb-0"><?php echo e(v1SubjectAreaByCode($subject->subject_code)); ?> (<?php echo e(v1SubjectNameByCode($subject->subject_code)); ?>)</h6>

                        <span class="col-lg-2 col-md-2 col-4 pl-0">Proficiency:</span>
                        <h6 class="col-lg-2 col-md-2 col-8 pr-0 mb-0"><?php echo e($subject->proficiency); ?></h6>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <li class="list-group-item d-flex">No data found</li>
            <?php endif; ?>
        </ul>
    </div>
<?php endif; ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/educator/overview-steps/subjects.blade.php ENDPATH**/ ?>