
<div class="card">
    <div class="card-header">User Programs</div>
    <div class="card-body p-0">
        <?php if($programs->count() > 0): ?>
        <div class="table-responsive">
            <table class="table table-striped table-bordered mb-0">
                <thead class="thead-dark">
                    <tr>
                        <th scope="col" style="text-align: left;">Program Name</th>
                        <th scope="col" style="text-align: left;">Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($program->name); ?></td>
                            <td><?php echo e($program->status); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
            <p class="text-muted m-3">No programs added yet.</p>
        <?php endif; ?>
    </div>
</div>
<?php $__env->startSection('scripts'); ?>
<?php $__env->stopSection(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/schools/tabs/user-programs.blade.php ENDPATH**/ ?>