<style>
    td:last-of-type td:first-of-type {
        width: auto;
    }
    td:first-of-type {
        width: calc((60%));
    }
    table tbody tr:nth-of-type(even) {
        background-color: transparent !important;
    }

    #contracthtml {
        height: 400px;
        overflow-y: scroll;
        border: 1px solid gray;
        border-radius: 12px;
        padding: 1rem;
        resize: vertical;
        scrollbar-width: thin; /* For Firefox */
        scrollbar-color: #888 #f0f0f0;
    }

    ::-webkit-scrollbar {
        width: 10px; /* Scrollbar width */
        height: 10px; /* Scrollbar height for horizontal scroll */
    }
    
    ::-webkit-scrollbar-thumb {
        background: #888; /* Scrollbar thumb color */
        border-radius: 5px; /* Rounded edges */
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: #555; /* Scrollbar thumb color on hover */
    }
    
    ::-webkit-scrollbar-track {
    	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    	border-radius: 10px;
    	background-color: #F5F5F5;
    }
    
    ::-webkit-scrollbar-track:hover {
        background: #e0e0e0; /* Track color on hover */
    }

</style>
<?php
$sum=0;
?>
<?php if(request()->segment(4) == 'step7'): ?>
    <div class="card-body p-2 px-3">
        <?php if(!empty($user->educator)): ?>
            <?php
                $educator = explode(',', $user->educator);
            ?>
        <?php endif; ?>
        <ul class="list-group list-group-flush">
            
            <?php if(!empty($user->educator)): ?>  
                <?php if(in_array('marketplace educator', $educator) && in_array('whizara educator', $educator) && !empty($data['whizaraEducator']) && !empty($data['marketplaceEducator'])): ?>
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Contract:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                            <?php if(!empty($data['marketplaceEducator']->marketplace_contract)): ?>
                                <a target="_blank" href="<?php echo e(generateSignedUrl($data['marketplaceEducator']->marketplace_contract)); ?>">
                                    <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                                </a>
                            <?php endif; ?>
                        </h6>
                    </li>
                <?php elseif(in_array('whizara educator', $educator) && !empty($data['whizaraEducator'])): ?>
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Whizara Educator:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                            <?php if(!empty($data['whizaraEducator']->whizara_contract)): ?>
                                <a target="_blank" href="<?php echo e(generateSignedUrl($data['whizaraEducator']->whizara_contract)); ?>">
                                    <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                                </a>
                            <?php endif; ?>
                        </h6>
                    </li>
                <?php elseif(in_array('marketplace educator', $educator) && !empty($data['marketplaceEducator'])): ?>
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Marketplace Educator:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                            <?php if(!empty($data['marketplaceEducator']->marketplace_contract)): ?>
                                <a target="_blank" href="<?php echo e(generateSignedUrl($data['marketplaceEducator']->marketplace_contract)); ?>">
                                    <img src="<?php echo e(url('fileimg.png')); ?>" height="50px;" width="50px;">
                                </a>
                            <?php endif; ?>
                        </h6>
                    </li>
                <?php else: ?>
                    
                <?php endif; ?>
            <?php else: ?>
                <?php if(!empty($user->user_status) && ($user->user_status != 'Approved' && $user->user_status != 'Active')): ?>
                    <li class="list-group-item d-flex">No data found</li>
                <?php endif; ?>
            <?php endif; ?>
        </ul>
        <?php if(!empty($user->user_status) && ($user->user_status == 'Approved' || $user->user_status == 'Active')): ?>
            <div class="row loginarea__wraper">
                <div class="col-xl-12">
                    <div class="inner-form video Classroom">

                        <!-- Base Pay Section -->
                        <div class="col-12 mt-3 activate-wrng_main justify-content-around">
                            <?php
                                $bilingualTotal = $bilingual ?? 0;
                                $specialEducationAmmount = 0;
                                $specialEducationAmmount = $sped ?? 0;
                                $specialAmount = $sped ?? 0;
                            ?>
                            <div class="w-100 budget-container px-5">

                                <div class="row">
                                    <div class="col-md-6 pt-2 d-flex align-items-center">
                                        <label class="label_width text-nowrap pr-3 m-0" for="bilengue">Bi Lengue.</label>
                                        <input type="number" name="bilengue" class="form-control biLengue budget_input agreement_input" placeholder="Enter" value="<?php echo e($bilingualTotal); ?>" readonly>
                                        <?php $sum+=$bilingualTotal ?>
                                    </div>

                                    <div class="col-md-6 pt-2 d-flex align-items-center">
                                        <label class="label_width text-nowrap pr-3 m-0" for="case_management">Case Management</label>
                                        <input type="checkbox" name="case_managemnet_status" style="max-width: 16px; margin-right: 10px;" class="form-control case_management budget_input agreement_input" placeholder="Enter case management" <?php if($user->step3->case_management): ?> checked <?php endif; ?> disabled>
                                        <input type="number" name="case_managemnet" class="form-control case_management budget_input agreement_input" placeholder="Enter case management" value="<?php echo e($case_management ?? 0); ?>" readonly>
                                        <?php $sum+=$case_management ?>
                                    </div>

                                    
                                </div>
                                <?php if(!empty($specialEducation)): ?>
                                    <div class="row">
                                        <div class="col-12 pt-2 gap-3 d-flex align-items-center">
                                            <label class="mb-0 mr-3">Special Education</label>
                                            <input type="checkbox" id="sped_status" name="sped_status" style="max-width: 16px; margin-right: 10px;" checked class="form-control sped_edu budget_input" disabled>
                                        
                                            <ul class="ml-5">
                                                <?php $__currentLoopData = $specialEducation; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li> <?php echo e($key); ?>: 
                                                    <?php $__currentLoopData = $value; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if($loop->first): ?>
                                                            <?php $specialEducationAmmount = $v[1]; ?>
                                                        <?php endif; ?>
                                                        <?php echo e("{$v[0]} ({$v[1]}" . ($loop->last ? ')' : ', ')); ?>

                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> 
                                                    </li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>



                        <div>
                            <div class="row d-flex justify-content-center mt-2 px-5">
                                <table class="table table-bordered budget_table">
                                    <thead>
                                        <tr>
                                            <th>Check</th>
                                            <th>Subject</th>
                                            <th>Base Pay</th>
                                            <th>Experience</th>
                                            <th>Education</th>
                                            <th>Non Teaching</th>
                                            <th>Special Education</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody class="budget_table">
                                        <?php $__currentLoopData = $subjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $approvedBudgetData = $approvedBudgetLines->where('subject_code', $subject->subject_code)->first();
                                            ?>

                                            <tr>
                                                <td class="text-center align-middle">
                                                    <input type="checkbox"
                                                        name="selected[<?php echo e($index); ?>]"
                                                        id="selected_<?php echo e($index); ?>"
                                                        checked
                                                        class="agreement_input"
                                                        style="width:20px; height:15px;" disabled>
                                                </td>
                                                <td style="min-width: 300px;">
                                                    <input type="text"
                                                        name="code_title[<?php echo e($index); ?>]"
                                                        id="code_title_<?php echo e($index); ?>"
                                                        class="form-control agreement_input"
                                                        value="<?php echo e($subject->title); ?>"
                                                        readonly>
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="base_pay[<?php echo e($index); ?>]"
                                                        id="base_pay_<?php echo e($index); ?>"
                                                        class="form-control agreement_input"
                                                        data-field="base_pay"
                                                        value="<?php echo e($approvedBudgetData ? $approvedBudgetData->base_pay : 0); ?>" readonly>
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="experience_pay[<?php echo e($index); ?>]"
                                                        id="experience_pay_<?php echo e($index); ?>"
                                                        class="form-control agreement_input"
                                                        data-field="experience_pay"
                                                        value="<?php echo e($approvedBudgetData ? $approvedBudgetData->experience_pay : 0); ?>" readonly>
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="education_pay[<?php echo e($index); ?>]"
                                                        id="education_pay_<?php echo e($index); ?>"
                                                        class="form-control agreement_input"
                                                        data-field="education_pay"
                                                        value="<?php echo e($approvedBudgetData ? $approvedBudgetData->education_pay : 0); ?>" readonly>
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="non_teaching[<?php echo e($index); ?>]"
                                                        id="non_teaching_<?php echo e($index); ?>"
                                                        class="form-control agreement_input"
                                                        data-field="non_teaching"
                                                        value="<?php echo e($approvedBudgetData ? $approvedBudgetData->non_teaching : 0); ?>" readonly>
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="special_education[<?php echo e($index); ?>]"
                                                        id="special_education_<?php echo e($index); ?>"
                                                        class="form-control agreement_input"
                                                        data-field="special_education"
                                                        value="<?php echo e($approvedBudgetData ? $approvedBudgetData->special_education : 0); ?>" readonly>
                                                </td>
                                                <td>
                                                    <input type="number"
                                                        name="total[<?php echo e($index); ?>]"
                                                        id="total_<?php echo e($index); ?>"
                                                        class="form-control agreement_input"
                                                        data-field="total"
                                                        value="<?php echo e($approvedBudgetData ? $approvedBudgetData->total : 0); ?>"
                                                        readonly>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>

                                </table>


                            </div>
                            

                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/application-steps/step-7.blade.php ENDPATH**/ ?>