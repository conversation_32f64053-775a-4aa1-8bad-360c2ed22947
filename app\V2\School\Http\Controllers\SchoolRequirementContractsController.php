<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\SchoolRequirementContract;
use App\SchoolRequirementContractVersion;
use App\Models\v1\SchoolUser;
use App\V2\Core\Helpers\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Exception;

class SchoolRequirementContractsController extends Controller
{
    /**
     * Get the authenticated school user
     */
    private function getAuthenticatedUser()
    {
        // Try to get user from platform_school guard first
        if (Auth::guard('platform_school')->check()) {
            return Auth::guard('platform_school')->user();
        }

        // Fallback to session-based authentication
        $sessionData = session('schoolloginsession');
        if (!$sessionData || !isset($sessionData['id'])) {
            return null;
        }
        return SchoolUser::find($sessionData['id']);
    }

    /**
     * Check if user is authenticated and return error response if not
     */
    private function checkAuthentication()
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return ApiResponse::error("User not authenticated", 401);
        }
        return null; // No error, user is authenticated
    }
    /**
     * Get all requirement contracts for a school.
     *
     * GET /api/schools/requirements/contracts
     */
    public function index(Request $request)
    {
        try {
            // Check authentication
            $authError = $this->checkAuthentication();
            if ($authError) {
                return $authError;
            }

            $user = $this->getAuthenticatedUser();
            // Get contracts through the requirement relationship since there's no direct school_id
            $contracts = SchoolRequirementContract::with(['requirement', 'createdBy', 'updatedBy', 'versions'])
                ->whereHas('requirement', function($query) use ($user) {
                    $query->where('school_id', $user->school_id);
                })
                ->orderBy('created_at', 'desc')
                ->get();

            return ApiResponse::success($contracts, "Contracts fetched successfully");
        } catch (Exception $e) {
            Log::error('Error fetching contracts: ' . $e->getMessage());
            return ApiResponse::error('Failed to fetch contracts', 500);
        }
    }

    /**
     * Get a specific requirement contract for a school.
     *
     * GET /api/schools/requirements/contracts/{id}
     */
    public function show($id)
    {
        try {
            // Check authentication
            $authError = $this->checkAuthentication();
            if ($authError) {
                return $authError;
            }

            $user = $this->getAuthenticatedUser();

            // Find contract with relationships
            $contract = SchoolRequirementContract::with(['requirement', 'createdBy', 'updatedBy', 'versions'])
                ->whereHas('requirement', function($query) use ($user) {
                    $query->where('school_id', $user->school_id);
                })
                ->find($id);

            if (!$contract) {
                return ApiResponse::error('Contract not found', 404);
            }

            return ApiResponse::success($contract, "Contract retrieved successfully");

        } catch (Exception $e) {
            Log::error('Error fetching contract: ' . $e->getMessage());
            return ApiResponse::error('Failed to fetch contract', 500);
        }
    }

    /**
     * Create a new requirement contract for a school.
     *
     * POST /api/schools/requirements/contracts
     */
    public function store(Request $request)
    {
        try {
            // Check authentication first
            $authError = $this->checkAuthentication();
            if ($authError) {
                return $authError;
            }
            $user = $this->getAuthenticatedUser();
            DB::beginTransaction();
            // Enhanced validation with better rules
            $validator = Validator::make($request->all(), [
                'requirement_id'      => 'nullable|exists:platform_school_requirements,id',
                'legal_first_name'    => 'nullable|string|max:255',
                'legal_last_name'     => 'nullable|string|max:255',
                'phone'               => 'nullable|string|max:50',
                'email'               => 'nullable|email|max:255',
                'job_title'           => 'nullable|string|max:255',
                'address'             => 'nullable|string|max:1000',
                'client_name'         => 'nullable|string|max:255',
                'has_purchase_order'  => 'nullable|boolean',
                'purchase_order_ref'  => 'nullable|string|max:255',
                'status'              => 'nullable|in:draft,pending_approval,in_review,approved,on_hold,cancelled,completed',

                // version table
                'document'            => 'nullable|file|mimes:pdf,doc,docx,txt|max:20480', // 20MB max
                'version_number'      => 'nullable|string|max:50',
                'notes'               => 'nullable|string|max:2000',
            ], [
                'document.mimes' => 'Document must be a PDF, DOC, DOCX, or TXT file.',
                'document.max' => 'Document size must not exceed 20MB.',
                'email.email' => 'Please provide a valid email address.',
                'requirement_id.exists' => 'The selected requirement does not exist.',
            ]);

            if ($validator->fails()) {
                return ApiResponse::error('Validation failed', 422, $validator->errors());
            }

            // Prepare contract data with defaults and validation
            $contractData = $request->only([
                'requirement_id',
                'legal_first_name',
                'legal_last_name',
                'phone',
                'email',
                'job_title',
                'address',
                'client_name',
                'has_purchase_order',
                'purchase_order_ref',
                'status',
            ]);

            // Set default values for required fields
            $contractData['status'] = $contractData['status'] ?? 'draft';
            $contractData['has_purchase_order'] = $contractData['has_purchase_order'] ?? false;
            // Add polymorphic audit fields using authenticated user
            $contractData['created_by_id']   = $user->id;
            $contractData['created_by_type'] = get_class($user);
            $contractData['updated_by_id']   = $user->id;
            $contractData['updated_by_type'] = get_class($user);
            // Create contract
            $contract = SchoolRequirementContract::create($contractData);
            // Handle document upload with enhanced error handling
            $fileUrl = null;
            if ($request->hasFile('document')) {
                $file = $request->file('document');

                // Validate file
                if (!$file->isValid()) {
                    DB::rollBack();
                    return ApiResponse::error('Invalid file upload', 422);
                }

                try {
                    $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                    $extension = $file->getClientOriginalExtension();
                    $sanitizedName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $originalName);
                    $name = time() . '-' . $sanitizedName . '.' . $extension;
                    $filename = 'uploads/school/contracts/' . $name;

                    // Upload to S3
                    uploads3image($filename, $file);
                    $fileUrl = $filename;

                    Log::info('Document uploaded successfully', ['filename' => $filename, 'original_name' => $file->getClientOriginalName(), 'size' => $file->getSize()]);
                } catch (Exception $uploadException) {
                    DB::rollBack();
                    Log::error('File upload failed: ' . $uploadException->getMessage());
                    return ApiResponse::error('File upload failed. Please try again.', 500);
                }
            }
            
            // Prepare version data
            $versionData = $request->only(['version_number', 'notes']);
            // Set defaults and required fields
            $versionData['school_requirement_contract_id'] = $contract->id;
            $versionData['file_url'] = $fileUrl;
            $versionData['version_number'] = $versionData['version_number'] ?? 'v1.0';
            $versionData['created_by_id']   = $user->id;
            $versionData['created_by_type'] = get_class($user);
            $versionData['updated_by_id']   = $user->id;
            $versionData['updated_by_type'] = get_class($user);
            // Create version record
            $version = SchoolRequirementContractVersion::create($versionData);

            DB::commit();
            // Load relationships for response
            $contract->load(['requirement', 'createdBy', 'updatedBy', 'versions']);
            return ApiResponse::success(['contract' => $contract, 'message' => 'Contract created successfully', 'has_document' => !is_null($fileUrl)], "Contract created successfully");
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error creating contract: ' . $e->getMessage(), ['user_id' => $user->id ?? 'unknown', 'request_data' => $request->except(['document']), 'trace' => $e->getTraceAsString()]);
            return ApiResponse::error('Failed to create contract. Please try again later.', 500);
        }
    }

    /**
     * Update a specific requirement contract for a school.
     *
     * PUT /api/schools/requirements/contracts/{id}
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Delete a specific requirement contract for a school.
     *
     * DELETE /api/schools/requirements/contracts/{id}
     */
    public function destroy($id)
    {
        //
    }
}
