<?php

namespace App\V2\School\Http\Controllers;

use App\Models\v1\SchoolBudget;
use App\Models\v1\SchoolSubjectBudget;
use App\Http\Controllers\Controller;
use App\Models\School\SchoolClassBudget;
use App\Models\v1\Subject;
use Illuminate\Validation\ValidationException;
use App\V2\Core\Helpers\ApiResponse;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class CalculateBudgetController extends Controller
{
    // ******************Fetch All Budget******************
    public function index()
    {
        $budgets = SchoolClassBudget::with(['school:id,first_name,last_name', 'subject', 'subject.subjectArea'])->where('school_id', Auth::user()->id)->get();
        return ApiResponse::success($budgets, "Budgets fetched successfully");
    }
    // ******************Fetch All Budget******************

    // ******************Generate Name******************
    private function generateName($validated)
    {
        $subject = Subject::find($validated['subject']);
        $parts = [
            $validated['requirement_type'] ?? '',
            $subject->name ?? '',
            $validated['delivery_mode'] ?? '',
            $validated['language_of_instruction'] ?? '',
        ];
        return implode(' - ', array_filter($parts));
    }
    // ******************Generate Name******************

    // ******************Fetch All School Budget******************
    public function bySubject($subjectId)
    {
        $schoolBudget = SchoolSubjectBudget::with('schoolBudget')->where('subject_id', $subjectId)->first();
        return ApiResponse::success($schoolBudget, "Subject budget info fetched successfully");
    }
    // ******************Fetch All School Budget******************

    // ******************Create Budget******************
    public function store(Request $request)
    {
        // The `validate` method handles validation exceptions automatically.
        // If validation fails, it will return a 422 Unprocessable Entity response.
        $validated = $request->validate([
            'requirementType' => ['required', Rule::in(['class', 'caseManager', 'SLP'])],
            'subject' => 'nullable|string',
            'specialEducation' => ['nullable', Rule::in(['yes', 'no'])],
            'eSOLCertification' => ['nullable', Rule::in(['yes', 'no'])],
            'curriculum' => ['nullable', Rule::in(['true', 'false'])],
            'instructionalDays' => 'nullable|string',
            'classDuration' => 'nullable|string',
            'nonInstructionalHours' => 'nullable|integer|min:0',
            'languageOfInstruction' => 'nullable|string',
            'expectedClassSize' => 'nullable|integer|min:1',
            'numberOfDays' => 'nullable|integer|min:1',
            'numberOfhours' => 'nullable|integer|min:1',
            'caseLoad' => 'nullable|string',
        ]);
        $mappedData = [
            'school_id' => Auth::user()->school_id,
            'name' => $request->input('name') ?? $this->generateName($validated) ?? 'Class Budget',
            'requirement_type' => $validated['requirementType'],
            'subject_id' => $validated['subject'] ?? null,
            'special_education_certification' => $validated['specialEducation'] ?? null,
            'esol_certification' => $validated['eSOLCertification'] ?? null,
            'school_curriculum_provided' => $validated['curriculum'] == 'true' ?? null,
            'instructional_days' => $validated['instructionalDays'] ?? null,
            'class_duration_hours' => $validated['classDuration'] ?? null,
            'non_instructional_hours' => $validated['nonInstructionalHours'] ?? null,
            'language_of_instruction' => $validated['languageOfInstruction'] ?? null,
            'expected_class_size' => $validated['expectedClassSize'] ?? null,
            'number_of_days' => $validated['numberOfDays'] ?? null,
            'number_of_hours' => $validated['numberOfhours'] ?? null,
            'case_load' => $validated['caseLoad'] ?? null,
            'calculated_budget' => 0 ?? null,
        ];

        try {
            $mappedData['calculated_budget'] = $this->calculateBudget($mappedData);
            $budget = SchoolClassBudget::create($mappedData);
            return ApiResponse::success($budget, 'Budget saved successfully', 201);
        } catch (QueryException $e) {
            // Return a 500 server error response with a user-friendly message
            return ApiResponse::error('A database error occurred while saving the budget.', 500, $e);
        } catch (\Exception $e) {
            // Catch any other unexpected exceptions
            return ApiResponse::error('An unexpected error occurred.', 500, $e);
        }
    }
    // ******************Create Budget******************

    // ******************Update Budget******************
    public function update(Request $request, $id)
    {
        $budget = SchoolClassBudget::where('school_id', Auth::user()->school_id)->findOrFail($id);

        $validated = $request->validate([
            'requirementType' => ['required', Rule::in(['class', 'caseManager', 'SLP'])],
            'subject' => 'nullable|string',
            'specialEducation' => ['nullable', Rule::in(['yes', 'no'])],
            'eSOLCertification' => ['nullable', Rule::in(['yes', 'no'])],
            'curriculum' => ['nullable', Rule::in(['true', 'false'])],
            'instructionalDays' => 'nullable|string',
            'classDuration' => 'nullable|string',
            'nonInstructionalHours' => 'nullable|integer|min:0',
            'languageOfInstruction' => 'nullable|string',
            'expectedClassSize' => 'nullable|integer|min:1',
            'numberOfDays' => 'nullable|integer|min:1',
            'numberOfhours' => 'nullable|integer|min:1',
            'caseLoad' => 'nullable|string',
        ]);
        $mappedData = [
            'school_id' => Auth::user()->school_id,
            'name' => $request->input('name') ?? $this->generateName($validated) ?? 'Class Budget',
            'requirement_type' => $validated['requirementType'],
            'subject_id' => $validated['subject'] ?? null,
            'special_education_certification' => $validated['specialEducation'] ?? null,
            'esol_certification' => $validated['eSOLCertification'] ?? null,
            'school_curriculum_provided' => $validated['curriculum'] == 'true' ?? null,
            'instructional_days' => $validated['instructionalDays'] ?? null,
            'class_duration_hours' => $validated['classDuration'] ?? null,
            'non_instructional_hours' => $validated['nonInstructionalHours'] ?? null,
            'language_of_instruction' => $validated['languageOfInstruction'] ?? null,
            'expected_class_size' => $validated['expectedClassSize'] ?? null,
            'number_of_days' => $validated['numberOfDays'] ?? null,
            'number_of_hours' => $validated['numberOfhours'] ?? null,
            'case_load' => $validated['caseLoad'] ?? null,
            'calculated_budget' => 0 ?? null,
        ];
        $mappedData['name'] = $validated['name'] ?? $this->generateName(array_merge($budget->toArray(), $validated)) ?? $budget->name;
        try {
            $mappedData['calculated_budget'] = $this->calculateBudget($mappedData);
            $budget->update($mappedData);
            return ApiResponse::success($budget, 'Budget updated successfully');
        } catch (QueryException $e) {
            // Return a 500 server error response with a user-friendly message
            return ApiResponse::error('A database error occurred while saving the budget.', 500, $e);
        } catch (\Exception $e) {
            // Catch any other unexpected exceptions
            return ApiResponse::error('An unexpected error occurred.', 500, $e);
        }
    }
    // ******************Update Budget******************

    // ******************Delete Budget******************
    public function destroy($id)
    {
        $budget = SchoolClassBudget::where('school_id', Auth::user()->id)->findOrFail($id);
        $budget->delete();
        return ApiResponse::success([], 'Budget deleted successfully');
    }
    // ******************Delete Budget******************

    // ******************Duplicate Budget******************
    public function duplicate_budget(Request $request)
    {
        $budget_id = $request->id;
        $originalBudget = SchoolClassBudget::find($budget_id);

        if (!$originalBudget) {
            return ApiResponse::error('Source budget not found', 404);
        }

        $newBudget = $originalBudget->replicate(); // clone all attributes except id
        $newBudget->name = $originalBudget->name . ' Copy '; // change name
        $newBudget->save();

        return ApiResponse::success($newBudget, 'New budget created successfully');
    }
    // ******************Duplicate Budget******************

    public function calculateBudget($request)
    {
        $subject_budget = SchoolSubjectBudget::with('schoolBudget')
            ->where('subject_id', $request['subject_id'])
            ->first();

        // If requirement type is not class → budget is 0
        if (($request['requirement_type'] ?? null) !== 'class') {
            return 0;
        }

        // Base pay
        $basePay = $subject_budget->base_pay_0_3 ?? 0;

        // Curriculum increment
        $curriculumInc = !empty($request['school_curriculum_provided'])
            ? ($subject_budget->curriculum_inc ?? 0)
            : 0;

        // Bilingual increment (if language not English)
        $bilingualInc = (
            !empty($request['language_of_instruction']) &&
            strtolower($request['language_of_instruction']) !== 'english'
            )
            ? ($subject_budget->schoolBudget->bilingual_inc ?? 0)
            : 0;

        // Instructional days
        $days = $request['instructional_days'] ?? 0;

        // Parse class duration (e.g., "2.5hr" → 2.5)
        $durationRaw = $request['class_duration_hours'] ?? '0';
        $duration = floatval(str_replace('hr', '', strtolower($durationRaw)));

        // Final calculation
        $totalHours = $duration * ($days ?: 1);
        return ($basePay + $curriculumInc + $bilingualInc) * $totalHours;
    }
}