 <?php $__env->startSection('title'); ?>
View Educator | Whizara
<?php $__env->stopSection(); ?> <?php $__env->startSection('content'); ?>

<?php
    $res = get_permission(session('Adminnewlogin')['type']);
    $issetResManageApplication = isset($res['manageapplication']);
    if($issetResManageApplication){
        $arrResManageApplication = json_decode($res['manageapplication'], true);
    }else{
        $arrResManageApplication = [];
    }

    $issetResManageInstructor = isset($res['manageinstructor']);
    if($issetResManageInstructor){
        $arrResManageInstructor = json_decode($res['manageinstructor'] ,true);
    }else{
        $arrResManageInstructor = [];
    }
?>

<style>
    .card .card-body.signature {
        height: 450px;
        overflow-y: scroll;
        width: 90%;
        margin: 0 auto;
    }
    .pe-none {
        pointer-events: none;
    }
    .ans-text {
        width: calc(100%);
        border-radius: 12px;
        padding: 10px;
        resize: vertical;
        height: 110px;
    }
    .border-light {
        border-color: #cdd0d3 !important;
    }
    td:first-of-type {
        width: 0% !important;
    }
</style>

<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item active" aria-current="page">Educator List</li>
                <li class="breadcrumb-item active" aria-current="page"> View Educator</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->
        <!-- PROFILE DETAILS SECTION START -->
        <div class="row justify-content-center">
            <div class="col-lg-2 col-md-3 col-6 text-center">
                <div class="profile_img_box text-center mb-3">
                    <?php if($educator->instructor->image != null): ?>
                        <img src="<?php echo e(generateSignedUrl($educator->instructor->image)); ?>" alt="image">
                    <?php else: ?>
                        <img src="<?php echo e(default_user_placeholder('male')); ?>" alt="User" class="img-fluid">
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-10 col-md-9">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header border-bottom">
                                <h5 class="mb-0">Educator Details <b></b></h5>
                                <input type="hidden" name="educator_id" id="educator_id" value="<?php echo e($educator->id); ?>">
                            </div>
                            <div class="card-body p-0">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Educator Name:</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($educator->instructor->first_name . ' ' . $educator->instructor->last_name); ?></h6>
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">Email :</span>
                                        <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0"><?php echo e($educator->instructor->email); ?> &nbsp;
                                        <?php if($educator->instructor->email_verify_status == 1): ?>
                                            <span class="btn btn-success">Verified</span>
                                        <?php else: ?>
                                            <span class="btn btn-danger">Not Verified</span>
                                        <?php endif; ?>
                                        </h6>
                                    </li>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">City :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($educator->instructor->city); ?></h6>
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">State:</span>
                                        <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0"><?php echo e($educator->instructor->state); ?></h6>
                                    </li>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Zip :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($educator->instructor->zipcode); ?></h6>
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">Country:</span>
                                        <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0"> <?php echo e($educator->instructor->country); ?></h6>
                                    </li>

                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Source :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($educator->instructor->about); ?></h6>
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">Educator :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($educator->instructor->educator ?? 'Educator Not Defined'); ?></h6>
                                    </li>

                                    <li class="list-group-item d-flex">
                                        <?php
                                            $res = get_permission(session('Adminnewlogin')['type']);
                                            $perms = [];
                                            // Try multiple known keys for backward compatibility
                                            $keysToTry = ['manage_applicants', 'manageinstructor', 'manageapplication', 'manage_applicant'];
                                            foreach ($keysToTry as $k) {
                                                if (isset($res[$k])) { $perms = json_decode($res[$k] ?? '[]', true) ?: []; break; }
                                            }
                                            $normalizedPerms = array_map(function($v){ return strtolower(trim($v)); }, $perms);
                                            $canStatusChange = in_array('change status', $normalizedPerms);
                                        ?>
                                        <?php if($canStatusChange): ?>
                                        <div class="col-lg-3 col-md-3 col-4 pl-0 d-flex align-items-center gap-2">
                                            <input type="checkbox" <?php if(!empty($educator->instructor->is_background_check)): ?> checked <?php endif; ?> name="is_background_check" id="is_background_check" value="1" data-user-id='<?php echo e($educator->instructor->id); ?>'>
                                            <label class="pt-2 pl-2">Background Check</label>
                                        </div>
                                        <?php endif; ?>

                                        <label class="pt-2 px-3">Substitute</label>
                                        <div class="col-lg-3 col-md-3 col-3 pl-0 d-flex align-items-center gap-2">
                                            <select class="form-control" name="is_sub" id="is_sub">
                                                <option value="2" <?php if(!empty($educator->open_to_substitute_opportunity == 2)): ?> selected <?php endif; ?> >Hired as sub only</option>
                                                <option value="1" <?php if(!empty($educator->open_to_substitute_opportunity == 1)): ?> selected <?php endif; ?> >Open to sub roles</option>
                                                <option value="0" <?php if(!empty($educator->open_to_substitute_opportunity == 0)): ?> selected <?php endif; ?> >Not open to sub roles</option>
                                            </select>
                                        </div>
                                        <input type="hidden" name="user_id" id="user_id" value="<?php echo e($educator->user_id); ?>">

                                        <?php if($canStatusChange): ?>
                                            <div class="col-lg-4 col-md-4 pl-0 d-flex align-items-center gap-2">
                                                <label class="pt-2 px-3">Status:</label>
                                                <?php
                                                    $normalized = $normalizedPerms ?? [];
                                                    $canStateChangeOnlyLocal = in_array('state change only', $normalized) || in_array('approve/decline', $normalized);
                                                    $canAcceptApplicationLocal = in_array('accept application', $normalized) || in_array('approve & send contract', $normalized);
                                                    $canRejectApplicationLocal = in_array('reject application', $normalized) || in_array('decline & send notification', $normalized);
                                                    $status = $educator->instructor->user_status;

                                                    // Define all possible options with their labels and conditions
                                                    $options = [
                                                        'InProgress' => ['label' => 'Pending'],
                                                        'UnderReview' => ['label' => 'Under Review'],
                                                        'ChangeRequested' => ['label' => 'Request Change'],
                                                        'Unsure' => ['label' => 'Unsure'],
                                                        'ApprovedWithoutContract' => ['label' => 'Approve', 'condition' => $canStateChangeOnlyLocal],
                                                        'RejectWithoutNotification' => ['label' => 'Decline', 'condition' => $canStateChangeOnlyLocal],
                                                        'Approved' => ['label' => 'Approve & Send Contract', 'condition' => $canAcceptApplicationLocal],
                                                        'Withdraw' => ['label' => 'Withdraw', 'condition' => $canAcceptApplicationLocal],
                                                        'Declined' => ['label' => 'Decline & Send Notification', 'condition' => $canRejectApplicationLocal],
                                                        'Active' => ['label' => 'Activate'],
                                                    ];

                                                    // Define the options available for each specific status.
                                                    $statusOptions = [
                                                        'InProgress' => ['InProgress'],
                                                        'UnderReview' => ['UnderReview', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved', 'Declined', 'Unsure'],
                                                        'Unsure' => ['Unsure', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved', 'Declined'],
                                                        'ChangeRequested' => ['ChangeRequested', 'RejectWithoutNotification', 'Declined'],
                                                        'Active' => ['Active', 'RejectWithoutNotification', 'Declined'],
                                                        'Declined' => ['Declined', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved'],
                                                        'Approved' => ['Approved', 'Withdraw', 'RejectWithoutNotification', 'Declined'],
                                                        'ApprovedWithoutContract' => ['ApprovedWithoutContract', 'ChangeRequested', 'Approved', 'RejectWithoutNotification', 'Declined'],
                                                        'RejectWithoutNotification' => ['RejectWithoutNotification', 'ChangeRequested', 'ApprovedWithoutContract', 'Approved', 'Declined'],
                                                    ];

                                                    // Get the allowed options for the current status
                                                    $allowedOptions = $statusOptions[$status] ?? [];
                                                ?>

                                                <select data-user-id='<?php echo e($educator->instructor->id); ?>' class="form-control" <?php if($status == 'InProgress'): ?> disabled <?php endif; ?> id="userStatus" data-toggle="select2" name="userStatus">
                                                    <?php $__currentLoopData = $allowedOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optionKey): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php
                                                            $optionData = $options[$optionKey];
                                                            $showOption = !isset($optionData['condition']) || $optionData['condition'];
                                                        ?>

                                                        <?php if($showOption): ?>
                                                            <option
                                                                value="<?php echo e($optionKey); ?>"
                                                                <?php if($optionKey == $status): ?> selected hidden <?php endif; ?>>
                                                                <?php echo e($optionData['label']); ?>

                                                            </option>
                                                        <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        <?php endif; ?>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- PROFILE DETAILS SECTION END -->

        <!-- STEPS SECTION START -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            
                            <?php if($issetResManageApplication): ?>
                                <?php if(in_array('US Work Authorization', $arrResManageApplication)): ?>
                                    <li class="breadcrumb-item " aria-current="page">
                                        <a href="<?php echo e(url('admin/k12connections/application/step1/')); ?>" class="
                                            <?php
                                                if (request()->segment(4) == 'step1') {
                                                    echo 'text-primary';
                                                }
                                            ?>">
                                            US Work Authorization
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            

                            
                            <?php if($issetResManageApplication): ?>
                                <?php if(in_array('Education & teaching experience', $arrResManageApplication)): ?>
                                    <li class="breadcrumb-item " aria-current="page">
                                        <a href="<?php echo e(url('admin/k12connections/application/step3/')); ?>" class="
                                            <?php
                                                if (request()->segment(4) == 'step3') {
                                                    echo 'text-primary';
                                                }
                                            ?>">
                                            Education & Experience
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            

                            
                            <?php if($issetResManageApplication): ?>
                                <?php if(in_array('Your Teaching Preferences', $arrResManageApplication)): ?>
                                    <li class="breadcrumb-item " aria-current="page">
                                        <a href="<?php echo e(url('admin/k12connections/application/step4/')); ?>" class="
                                            <?php
                                                if (request()->segment(4) == 'step4') {
                                                    echo 'text-primary';
                                                }
                                            ?>">
                                            Your Teaching Preferences
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            

                            
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="<?php echo e(url('admin/k12connections/application/step5/')); ?>" class="
                                    <?php
                                        if (request()->segment(4) == 'step5') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Profile
                                </a>
                            </li>
                            

                            
                            <?php if($issetResManageApplication): ?>
                                <?php if(in_array('Quiz', $arrResManageApplication)): ?>
                                    <li class="breadcrumb-item " aria-current="page">
                                        <a href="<?php echo e(url('admin/k12connections/application/step6/')); ?>" class="
                                            <?php
                                                if (request()->segment(4) == 'step6') {
                                                    echo 'text-primary';
                                                }
                                            ?>"
                                            >Free Responses and Quiz
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            

                            
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="<?php echo e(url('admin/k12connections/application/step7/')); ?>" class="
                                    <?php
                                        if (request()->segment(4) == 'step7') {
                                            echo 'text-primary';
                                        }
                                    ?>"
                                    >Agreement
                                </a>
                            </li>
                            

                            
                            <?php if($issetResManageApplication): ?>
                                <li class="breadcrumb-item " aria-current="page">
                                    <a href="<?php echo e(url('admin/k12connections/application/step8/')); ?>" class="
                                        <?php
                                            if (request()->segment(4) == 'step8') {
                                                echo 'text-primary';
                                            }
                                        ?>">
                                        Assesment
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                        </ol>
                        <ol class="breadcrumb">
                            
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="<?php echo e(url('admin/k12connections/manage-educator/'.$educator->user_id.'/availability')); ?>" class="
                                    <?php
                                        if (request()->segment(5) == 'availability') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Availability
                                </a>
                            </li>
                            

                            
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="<?php echo e(url('admin/k12connections/manage-educator/'.$educator->user_id.'/subjects')); ?>" class="
                                    <?php
                                        if (request()->segment(5) == 'subjects') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Subjects
                                </a>
                            </li>
                            

                            
                            

                            
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="<?php echo e(url('admin/k12connections/manage-educator/'.$educator->user_id.'/phone')); ?>" class="
                                    <?php
                                        if (request()->segment(5) == 'phone') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Phone Number
                                </a>
                            </li>
                            

                            
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="<?php echo e(url('admin/k12connections/manage-educator/'.$educator->user_id.'/notification')); ?>" class="
                                    <?php
                                        if (request()->segment(5) == 'notification') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Notification
                                </a>
                            </li>
                            

                            
                            <?php if(\Illuminate\Support\Str::contains($educator->instructor->teach, 'in-person')): ?>
                                <li class="breadcrumb-item" aria-current="page">
                                    <a href="<?php echo e(url('admin/k12connections/manage-educator/'.$educator->user_id.'/locations')); ?>" class="<?php echo e(request()->segment(5) == 'locations' ? 'text-primary' : ''); ?>">Locations</a>
                                </li>
                            <?php endif; ?>
                            
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
        <!-- STEPS SECTION END -->

        <!-- APPLICATION SECTION START -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    
                    <?php if($issetResManageApplication): ?>
                        <?php if(in_array('US Work Authorization', $arrResManageApplication)): ?>
                            <?php echo $__env->make('admin.marketplace.application-steps.step-1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    

                    
                    <?php if($issetResManageApplication): ?>
                        <?php if(in_array('Education & teaching experience', $arrResManageApplication)): ?>
                            <?php echo $__env->make('admin.marketplace.application-steps.step-3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    

                    
                    <?php if($issetResManageApplication): ?>
                        <?php if(in_array('Your Teaching Preferences', $arrResManageApplication)): ?>
                            <?php echo $__env->make('admin.marketplace.application-steps.step-4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    

                    
                    <?php echo $__env->make('admin.marketplace.application-steps.step-5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    

                    
                    <?php if($issetResManageApplication): ?>
                        <?php if(in_array('Quiz', $arrResManageApplication)): ?>
                            <?php echo $__env->make('admin.marketplace.application-steps.step-6', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    

                    
                    <?php echo $__env->make('admin.marketplace.application-steps.step-7', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    

                    
                    <?php echo $__env->make('admin.marketplace.application-steps.step-8', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    

                    
                    <?php echo $__env->make('admin.marketplace.educator.overview-steps.availability', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    

                    
                    <?php echo $__env->make('admin.marketplace.educator.overview-steps.subjects', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    

                    
                    

                    
                    <?php echo $__env->make('admin.marketplace.educator.overview-steps.phone', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    

                    
                    <?php echo $__env->make('admin.marketplace.educator.overview-steps.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    

                    
                    <?php echo $__env->make('admin.marketplace.educator.overview-steps.locations', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    
                </div>
            </div>
        </div>
        <!-- APPLICATION SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
  $(document).ready(function () {
    $('#is_sub').on('change', function () {
      const is_sub = $(this).val();
      const user_id = $('#user_id').val(); // Make sure this input exists

      $.ajax({
        url: '<?php echo e(route("admin.update-educator-substitute")); ?>',
        type: 'POST',
        data: {
          is_sub: is_sub,
          user_id: user_id,
          _token: '<?php echo e(csrf_token()); ?>'
        },
        beforeSend: function () {
          // Optionally show a loader
        },
        success: function (response) {
          if (response.success) {
            alertify.success('Substitute preference updated successfully.');
          } else {
            alertify.error(response.message || 'Failed to update.');
          }
        },
        error: function (xhr) {
          if (xhr.status === 422) {
            const errorMsg = xhr.responseJSON.message || 'Validation error occurred.';
            alertify.error('Validation error: ' + errorMsg);
          } else {
            alertify.error('An unexpected error occurred.');
          }
        }
      });
    });
  });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/educator/show.blade.php ENDPATH**/ ?>