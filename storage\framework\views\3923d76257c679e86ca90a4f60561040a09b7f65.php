 <?php $__env->startSection('title'); ?>
View Application | Whizara
<?php $__env->stopSection(); ?> <?php $__env->startSection('content'); ?>

<?php

$res = get_permission(session('Adminnewlogin')['type']);
$issetResManageApplication = isset($res['manageapplication']);
if($issetResManageApplication){

$arrResManageApplication = json_decode($res['manageapplication'], true);
}else{
$arrResManageApplication = [];

}

$issetResManageInstructor = isset($res['manageinstructor']);
if($issetResManageInstructor){

$arrResManageInstructor = json_decode($res['manageinstructor'] ,true);
}else{
$arrResManageInstructor = [];

}
?>

<style>
  .card .card-body.signature {
    height: 450px;
    overflow-y: scroll;
    width: 90%;
    margin: 0 auto;
  }

  .pe-none {
    pointer-events: none;
  }

  .ans-text {
    width: calc(100%);
    border-radius: 12px;
    padding: 10px;
    resize: vertical;
    height: 110px;
  }

  .border-light {
    border-color: #cdd0d3 !important;
  }

  .video_container {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1000;
    display: flex;
  }

  .video_container.hide {
    display: none;
  }

  .video_container::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
  }

  .video_container video {
    border-radius: 0.4rem;
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 50px);
  }

  .video_container span {
    right: 0;
    top: 0;
    transform: translate(60%, -60%);
    cursor: pointer;
    width: 20px;
    line-height: 20px;
  }

  td:first-of-type {
    width: 0% !important;
  }
</style>

<?php if($data["profile"] && $data["profile"]->video): ?>
<section id="video_demo" class="video_container justify-content-center align-items-center hide">
  <div class="position-relative">
    <video src="<?php echo e(generateSignedUrl($data['profile']->video)); ?>" controls></video>
    <span class="position-absolute d-flex justify-content-center align-items-center rounded-circle card m-0" onclick="showHideVideoDemo()"> X </span>
  </div>
</section>
<?php endif; ?>



<main class="content">
  <div class="container-fluid p-0">
    <!-- BREADCRUMB START -->
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page">Application List</li>
        <li class="breadcrumb-item active" aria-current="page"> View Application</li>
      </ol>
    </nav>
    <!-- BREADCRUMB END -->
    <!-- PROFILE DETAILS SECTION START -->
    <div class="row justify-content-center">
      <div class="col-lg-2 col-md-3 col-6 text-center">
        <div class="profile_img_box text-center mb-3">
          <?php if(!empty($data["profile"]) && !empty($data["profile"]->profile_image)): ?>
          <img src="<?php echo e(generateSignedUrl($data['profile']->profile_image)); ?>" alt="image">
          <?php else: ?>
          <img src="<?php echo e(default_user_placeholder('male')); ?>" alt="User" class="img-fluid">
          <?php endif; ?>
        </div>
        <?php if($data["profile"] && $data["profile"]->video): ?>
        <button onclick="showHideVideoDemo()" type="button" class="btn btn-light"> Video Demonstration </button>
        <?php endif; ?>
      </div>
      <div class="col-lg-10 col-md-9">
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
              <div class="card-header border-bottom">
                <h5 class="mb-0">Application Details <b></b></h5>
                <input type="hidden" name="application_id" id="application_id" value="<?php echo e($application_id); ?>">
              </div>
              <div class="card-body p-0">
                <ul class="list-group list-group-flush">
                  <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Instructor Name:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">
                      <?php echo e($user->first_name . ' ' . $user->last_name); ?>

                    </h6>
                    <span class="col-lg-2 col-md-2 col-4 pl-0">Email :</span>
                    <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0"><?php echo e($user->email); ?> &nbsp;
                      <?php if($user->email_verify_status == 1): ?> <span class="btn btn-success">Verified</span>
                      <?php else: ?>
                      <span class="btn btn-danger">Not Verified</span>
                      <?php endif; ?>
                    </h6>
                  </li>
                  <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">City :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($user->city); ?></h6>
                    <span class="col-lg-2 col-md-2 col-4 pl-0">State:</span>
                    <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0"><?php echo e($user->state); ?>

                    </h6>
                  </li>
                  <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Zip :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($user->zipcode); ?></h6>
                    <span class="col-lg-2 col-md-2 col-4 pl-0">Country:</span>
                    <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0"> <?php echo e($user->country); ?></h6>
                  </li>

                  <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Source :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($user->about); ?></h6>

                    <span class="col-lg-2 col-md-2 col-4 pl-0">Educator :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"><?php echo e($educator ?? 'Educator Not Defined'); ?></h6>
                  </li>

                  <li class="list-group-item d-flex">
                    <?php
                        $res = get_permission(session('Adminnewlogin')['type']);
                        $perms = [];
                        if (isset($res['manage_applicants'])) {
                            $perms = json_decode($res['manage_applicants'] ?? '[]', true) ?: [];
                        } elseif (isset($res['manageinstructor'])) {
                            $perms = json_decode($res['manageinstructor'] ?? '[]', true) ?: [];
                        }
                        $normalizedPerms = array_map(function($v){ return strtolower(trim($v)); }, $perms);
                        $canStatusChange = in_array('Change Status', $perms) || in_array('change status', array_map('strtolower', $perms));
                    ?>
                    <?php if($canStatusChange): ?>
                    <div class="col-lg-3 col-md-3 col-4 pl-0 d-flex align-items-center gap-2">
                      <input type="checkbox" <?php if(!empty($user->is_background_check)): ?> checked <?php endif; ?> name="is_background_check" id="is_background_check" value="1" data-user-id='<?php echo e($user->id); ?>'>
                      <label class="pt-2 pl-2">Background Check</label>
                    </div>

                    
                    <div class="col-lg-4 col-md-4 pl-0 d-flex align-items-center gap-2">
                      <label class="pt-2 px-3" for="is_sub">Substitute</label>
                      <select class="form-control" name="is_sub" id="is_sub">
                        <option value="2" <?php if(!empty($user->onboardingFinalization) && $user->onboardingFinalization->open_to_substitute_opportunity == 2): ?> selected <?php endif; ?>>Hire as sub only</option>
                        <option value="1" <?php if(!empty($user->onboardingFinalization) && $user->onboardingFinalization->open_to_substitute_opportunity == 1): ?> selected <?php endif; ?>>Open to sub role</option>
                        <option value="0" <?php if(!empty($user->onboardingFinalization) && $user->onboardingFinalization->open_to_substitute_opportunity == 0): ?> selected <?php endif; ?>>Not open to sub roles</option>
                      </select>
                    </div>
                    

                    <div class="col-lg-4 col-md-4 pl-0 d-flex align-items-center gap-2">
                        <label class="pt-2 px-3">Status:</label>
                        <?php
                            $normalized = $normalizedPerms ?? [];
                            $canStateChangeOnlyLocal = in_array('state change only', $normalized) || in_array('approve/decline', $normalized);
                            $canAcceptApplicationLocal = in_array('accept application', $normalized) || in_array('approve & send contract', $normalized);
                            $canRejectApplicationLocal = in_array('reject application', $normalized) || in_array('decline & send notification', $normalized);
                            $status = $user->user_status;

                            // Define all possible options with their labels and conditions
                            $options = [
                                'InProgress' => ['label' => 'Pending'],
                                'UnderReview' => ['label' => 'Under Review'],
                                'ChangeRequested' => ['label' => 'Request Change'],
                                'Unsure' => ['label' => 'Unsure'],
                                'ApprovedWithoutContract' => ['label' => 'Approve', 'condition' => $canStateChangeOnlyLocal],
                                'RejectWithoutNotification' => ['label' => 'Decline', 'condition' => $canStateChangeOnlyLocal],
                                'Approved' => ['label' => 'Approve & Send Contract', 'condition' => $canAcceptApplicationLocal],
                                'Withdraw' => ['label' => 'Withdraw', 'condition' => $canAcceptApplicationLocal],
                                'Declined' => ['label' => 'Decline & Send Notification', 'condition' => $canRejectApplicationLocal],
                                'Active' => ['label' => 'Activate'],
                            ];

                            // Define the options available for each specific status.
                            $statusOptions = [
                                'InProgress' => ['InProgress'],
                                'UnderReview' => ['UnderReview', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved', 'Declined', 'Unsure'],
                                'Unsure' => ['Unsure', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved', 'Declined'],
                                'ChangeRequested' => ['ChangeRequested', 'RejectWithoutNotification', 'Declined'],
                                'Active' => ['Active', 'RejectWithoutNotification', 'Declined'],
                                'Declined' => ['Declined', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved'],
                                'Approved' => ['Approved', 'Withdraw', 'RejectWithoutNotification', 'Declined'],
                                'ApprovedWithoutContract' => ['ApprovedWithoutContract', 'ChangeRequested', 'Approved', 'RejectWithoutNotification', 'Declined'],
                                'RejectWithoutNotification' => ['RejectWithoutNotification', 'ChangeRequested', 'ApprovedWithoutContract', 'Approved', 'Declined'],
                            ];

                            // Get the allowed options for the current status
                            $allowedOptions = $statusOptions[$status] ?? [];
                        ?>

                        <select data-user-id='<?php echo e($user->id); ?>' class="form-control" <?php if($status == 'InProgress'): ?> disabled <?php endif; ?> id="userStatus" data-toggle="select2" name="userStatus">
                            <?php $__currentLoopData = $allowedOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optionKey): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $optionData = $options[$optionKey];
                                    $showOption = !isset($optionData['condition']) || $optionData['condition'];
                                ?>

                                <?php if($showOption): ?>
                                    <option
                                        value="<?php echo e($optionKey); ?>"
                                        <?php if($optionKey == $status): ?> selected hidden <?php endif; ?>>
                                        <?php echo e($optionData['label']); ?>

                                    </option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <?php endif; ?>
                  </li>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- PROFILE DETAILS SECTION END -->

    <div class="row">
      <div class="col-lg-12">
        <div class="card">
          <div class="card-header border-bottom">
            <!-- <h5 class="mb-0">Steps details</h5> -->
          </div>
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
              <!-- <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li> -->

              
              <?php if($issetResManageApplication): ?>
              <?php if(in_array('US Work Authorization', $arrResManageApplication)): ?>
              <li class="breadcrumb-item " aria-current="page">
                <a href="<?php echo e(url('admin/k12connections/application/step1/')); ?>" class="
            <?php if (request()->segment(4) == 'step1') {
              echo 'text-primary';
            } ?>">US Work Authorization </a>
              </li>
              <?php endif; ?>
              <?php endif; ?>

              

              
              <?php if($issetResManageApplication): ?>
              <?php if(in_array('Education & teaching experience', $arrResManageApplication)): ?>
              <li class="breadcrumb-item " aria-current="page">
                <a href="<?php echo e(url('admin/k12connections/application/step3/')); ?>" class="
<?php if (request()->segment(4) == 'step3') {
  echo 'text-primary';
} ?>">Education & Experience </a>
              </li>
              <?php endif; ?>
              <?php endif; ?>

              

              
              <?php if($issetResManageApplication): ?>
              <?php if(in_array('Your Teaching Preferences', $arrResManageApplication)): ?>
              <li class="breadcrumb-item " aria-current="page">
                <a href="<?php echo e(url('admin/k12connections/application/step4/')); ?>" class="
<?php if (request()->segment(4) == 'step4') {
  echo 'text-primary';
} ?>">Your Teaching Preferences </a>
              </li>
              <?php endif; ?>
              <?php endif; ?>

              
              
              <li class="breadcrumb-item " aria-current="page">
                <a href="<?php echo e(url('admin/k12connections/application/step5/')); ?>" class="
<?php if (request()->segment(4) == 'step5') {
  echo 'text-primary';
} ?>">Profile </a>
              </li>

              

              
              <?php if($issetResManageApplication): ?>
              <?php if(in_array('Quiz', $arrResManageApplication)): ?>
              <li class="breadcrumb-item " aria-current="page">
                <a href="<?php echo e(url('admin/k12connections/application/step6/')); ?>" class="
<?php if (request()->segment(4) == 'step6') {
  echo 'text-primary';
} ?>">Free Responses and Quiz</a>
              </li>
              <?php endif; ?>
              <?php endif; ?>

              

              
              <li class="breadcrumb-item " aria-current="page">
                <a href="<?php echo e(url('admin/k12connections/application/step7/')); ?>" class="
               <?php if (request()->segment(4) == 'step7') {
                  echo 'text-primary';
                } ?>">Agreement </a>
              </li>
              
              <?php if($issetResManageApplication): ?>

              <li class="breadcrumb-item " aria-current="page">
                <a href="<?php echo e(url('admin/k12connections/application/step8/')); ?>" class="
                  <?php if (request()->segment(4) == 'step8') {
                    echo 'text-primary';
                  } ?>">Assesment</a>
              </li>

              <?php endif; ?>





              
            </ol>
          </nav>
        </div>
      </div>
    </div>


    <div class="row">
      <div class="col-lg-12">
        <div class="card">
          
          <?php if($issetResManageApplication): ?>
          <?php if(in_array('US Work Authorization', $arrResManageApplication)): ?>
          <?php echo $__env->make('admin.marketplace.application-steps.step-1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
          <?php endif; ?>
          <?php endif; ?>
          

          
          <?php if($issetResManageApplication): ?>
          <?php if(in_array('Education & teaching experience', $arrResManageApplication)): ?>
          <?php echo $__env->make('admin.marketplace.application-steps.step-3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
          <?php endif; ?>
          <?php endif; ?>
          

          
          <?php if($issetResManageApplication): ?>
          <?php if(in_array('Your Teaching Preferences', $arrResManageApplication)): ?>
          <?php echo $__env->make('admin.marketplace.application-steps.step-4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
          <?php endif; ?>
          <?php endif; ?>
          

          
          <?php echo $__env->make('admin.marketplace.application-steps.step-5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
          

          
          <?php if($issetResManageApplication): ?>
          <?php if(in_array('Quiz', $arrResManageApplication)): ?>
          <?php echo $__env->make('admin.marketplace.application-steps.step-6', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
          <?php endif; ?>
          <?php endif; ?>
          

          
          <?php echo $__env->make('admin.marketplace.application-steps.step-7', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
          
          <?php echo $__env->make('admin.marketplace.application-steps.step-8', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
      </div>

    </div>

    <input type="hidden" id="user_id" value="<?php echo e($application_id); ?>">


</main>


<!-- MAIN SECTION END -->

<?php $__env->stopSection(); ?> <?php $__env->startSection('scripts'); ?>
<script>
  function add_more() {
    var html = `<div class="row after-add-more" style="margin-left:0 !important;">
<div class="col-md-2 form-group">
<input type="hidden" name="scheduledid[]" value="">
<input type="date" class="form-control" name="date[]" id="date" placeholder="Enter date">
<span id="name_error" class="err"></span>
</div>
<div class="col-md-2 form-group">
<input type="time" class="form-control" name="time[]" id="time" placeholder="Enter time">
<span id="name_error" class="err"></span>
</div>
<div class="col-md-2 form-group">
<input type="time" class="form-control" name="end_time[]" id="end_time" placeholder="Enter end time">
<span id="name_error" class="err"></span>
</div>
<div class="col-md-2 form-group">
<select class="form-control" id="timezone" data-toggle="select2" name="timezone[]">
<option value="">Select Timezone</option>
<option value="Etc/GMT+12">(GMT-12:00) International Date Line West</option>
<option value="Pacific/Midway">(GMT-11:00) Midway Island, Samoa</option>
<option value="Pacific/Honolulu">(GMT-10:00) Hawaii</option>
<option value="US/Alaska">(GMT-09:00) Alaska</option>
<option value="America/Los_Angeles">(GMT-08:00) Pacific Time (US & Canada)</option>
<option value="America/Tijuana">(GMT-08:00) Tijuana, Baja California</option>
<option value="US/Arizona">(GMT-07:00) Arizona</option>
<option value="America/Chihuahua">(GMT-07:00) Chihuahua, La Paz, Mazatlan</option>
<option value="US/Mountain">(GMT-07:00) Mountain Time (US & Canada)</option>
<option value="America/Managua">(GMT-06:00) Central America</option>
<option value="US/Central">(GMT-06:00) Central Time (US & Canada)</option>
<option value="America/Mexico_City">(GMT-06:00) Guadalajara, Mexico City, Monterrey</option>
<option value="Canada/Saskatchewan">(GMT-06:00) Saskatchewan</option>
<option value="America/Bogota">(GMT-05:00) Bogota, Lima, Quito, Rio Branco</option>
<option value="US/Eastern">(GMT-05:00) Eastern Time (US & Canada)</option>
<option value="US/East-Indiana">(GMT-05:00) Indiana (East)</option>
<option value="Canada/Atlantic">(GMT-04:00) Atlantic Time (Canada)</option>
<option value="America/Caracas">(GMT-04:00) Caracas, La Paz</option>
<option value="America/Manaus">(GMT-04:00) Manaus</option>
<option value="America/Santiago">(GMT-04:00) Santiago</option>
<option value="Canada/Newfoundland">(GMT-03:30) Newfoundland</option>
<option value="America/Sao_Paulo">(GMT-03:00) Brasilia</option>
<option value="America/Argentina/Buenos_Aires">(GMT-03:00) Buenos Aires, Georgetown</option>
<option value="America/Godthab">(GMT-03:00) Greenland</option>
<option value="America/Montevideo">(GMT-03:00) Montevideo</option>
<option value="America/Noronha">(GMT-02:00) Mid-Atlantic</option>
<option value="Atlantic/Cape_Verde">(GMT-01:00) Cape Verde Is.</option>
<option value="Atlantic/Azores">(GMT-01:00) Azores</option>
<option value="Africa/Casablanca">(GMT+00:00) Casablanca, Monrovia, Reykjavik</option>
<option value="Etc/Greenwich">(GMT+00:00) Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London</option>
<option value="Europe/Amsterdam">(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</option>
<option value="Europe/Belgrade">(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague</option>
<option value="Europe/Brussels">(GMT+01:00) Brussels, Copenhagen, Madrid, Paris</option>
<option value="Europe/Sarajevo">(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb</option>
<option value="Africa/Lagos">(GMT+01:00) West Central Africa</option>
<option value="Asia/Amman">(GMT+02:00) Amman</option>
<option value="Europe/Athens">(GMT+02:00) Athens, Bucharest, Istanbul</option>
<option value="Asia/Beirut">(GMT+02:00) Beirut</option>
<option value="Africa/Cairo">(GMT+02:00) Cairo</option>
<option value="Africa/Harare">(GMT+02:00) Harare, Pretoria</option>
<option value="Europe/Helsinki">(GMT+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius</option>
<option value="Asia/Jerusalem">(GMT+02:00) Jerusalem</option>
<option value="Europe/Minsk">(GMT+02:00) Minsk</option>
<option value="Africa/Windhoek">(GMT+02:00) Windhoek</option>
<option value="Asia/Kuwait">(GMT+03:00) Kuwait, Riyadh, Baghdad</option>
<option value="Europe/Moscow">(GMT+03:00) Moscow, St. Petersburg, Volgograd</option>
<option value="Africa/Nairobi">(GMT+03:00) Nairobi</option>
<option value="Asia/Tbilisi">(GMT+03:00) Tbilisi</option>
<option value="Asia/Tehran">(GMT+03:30) Tehran</option>
<option value="Asia/Muscat">(GMT+04:00) Abu Dhabi, Muscat</option>
<option value="Asia/Baku">(GMT+04:00) Baku</option>
<option value="Asia/Yerevan">(GMT+04:00) Yerevan</option>
<option value="Asia/Kabul">(GMT+04:30) Kabul</option>
<option value="Asia/Yekaterinburg">(GMT+05:00) Yekaterinburg</option>
<option value="Asia/Karachi">(GMT+05:00) Islamabad, Karachi, Tashkent</option>
<option value="Asia/Calcutta">(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi</option>
<option value="Asia/Calcutta">(GMT+05:30) Sri Jayawardenapura</option>
<option value="Asia/Katmandu">(GMT+05:45) Kathmandu</option>
<option value="Asia/Almaty">(GMT+06:00) Almaty, Novosibirsk</option>
<option value="Asia/Dhaka">(GMT+06:00) Astana, Dhaka</option>
<option value="Asia/Rangoon">(GMT+06:30) Yangon (Rangoon)</option>
<option value="Asia/Bangkok">(GMT+07:00) Bangkok, Hanoi, Jakarta</option>
<option value="Asia/Krasnoyarsk">(GMT+07:00) Krasnoyarsk</option>
<option value="Asia/Hong_Kong">(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi</option>
<option value="Asia/Kuala_Lumpur">(GMT+08:00) Kuala Lumpur, Singapore</option>
<option value="Asia/Irkutsk">(GMT+08:00) Irkutsk, Ulaan Bataar</option>
<option value="Australia/Perth">(GMT+08:00) Perth</option>
<option value="Asia/Taipei">(GMT+08:00) Taipei</option>
<option value="Asia/Tokyo">(GMT+09:00) Osaka, Sapporo, Tokyo</option>
<option value="Asia/Seoul">(GMT+09:00) Seoul</option>
<option value="Asia/Yakutsk">(GMT+09:00) Yakutsk</option>
<option value="Australia/Adelaide">(GMT+09:30) Adelaide</option>
<option value="Australia/Darwin">(GMT+09:30) Darwin</option>
<option value="Australia/Brisbane">(GMT+10:00) Brisbane</option>
<option value="Australia/Canberra">(GMT+10:00) Canberra, Melbourne, Sydney</option>
<option value="Australia/Hobart">(GMT+10:00) Hobart</option>
<option value="Pacific/Guam">(GMT+10:00) Guam, Port Moresby</option>
<option value="Asia/Vladivostok">(GMT+10:00) Vladivostok</option>
<option value="Asia/Magadan">(GMT+11:00) Magadan, Solomon Is., New Caledonia</option>
<option value="Pacific/Auckland">(GMT+12:00) Auckland, Wellington</option>
<option value="Pacific/Fiji">(GMT+12:00) Fiji, Kamchatka, Marshall Is.</option>
<option value="Pacific/Tongatapu">(GMT+13:00) Nuku'alofa</option>
</select>
</div>
<div class="col-md-1 form-group change">
<a class='btn btn-danger remove'>-</a>
</div>
</div>`;
    $(".after-add-more").last().after(html);
    $(html).find(".change").html(" < a class = 'btn btn-danger remove' > - < /a>");
  }
  $("body").on("click", ".remove", function() {
    $(this).parents(".after-add-more").remove();
  });

  function delete_more(id) {
    var url = base_url + 'delete-interview';
    confirm_message = 'Are you sure you want to delete ?';
    delete_status(id, url, confirm_message);
  }

  function cancelinterview(id) {
    var url = base_url + 'cancel-interview';
    confirm_message = 'Are you sure you want to cancel interview?';
    delete_status(id, url, confirm_message);
  }

  $("#printbtn").click(function() {

    debugger;
    let mywindow = window.open('', 'PRINT', 'height=1754,width=1241,top=100,left=150');

    mywindow.document.write(`<html><head><title>Contract pdf</title>`);
    mywindow.document.write('</head><body >');
    mywindow.document.write(document.getElementById('printarea').innerHTML);
    mywindow.document.write('</body></html>');

    mywindow.document.close(); // necessary for IE >= 10
    mywindow.focus(); // necessary for IE >= 10*/

    mywindow.print();
    mywindow.close();

    return true;
  });



  function add_more_md() {



    var html = ` <div class="row after-add-more-md" style="margin-left:0 !important;">

<div class="col-md-2 form-group">

<input type="text" class="form-control" name="state[]" id="state" placeholder="State">
<span id="state_error" class="err"></span>
</div>

<div class="col-md-2 form-group">

<input type="text" class="form-control" name="description[]" id="description" placeholder="Description">
<span id="description_error" class="err"></span>
</div>

<div class="col-md-2 form-group">
<select class="form-control" name="form[]" id="form">
<option value="">Select form </option>
</select>

<span id="p_last_name_error" class="err"></span>
</div>



<div class="col-md-2 form-group">

<input type="text" class="form-control" name="instructions[]"  placeholder="Instructions">
<span id="instructions_errors" class="err"></span>
</div>



<div class="col-md-2 form-group">

<input type="date" class="form-control" name="deadline[]" id="deadline" placeholder="Deadline to complete">
<span id="Deadline_error" class="err"></span>
</div>

<div class="col-md-2 form-group">
<select class="form-control" name="school[]" id="school">
<option value="">Select school </option>

</select>

<span id="school_name_error" class="err"></span>
</div>
<div class="col-md-1 form-group changemd">
<a class='btn btn-danger removemd'>-</a>
</div>
</div>`;



    $(".after-add-more-md").last().after(html);
    $(html).find(".change").html(" < a class = 'btn btn-danger removemd' > - < /a>");
  }
  $("body").on("click", ".removemd", function() {
    $(this).parents(".after-add-more-md").remove();
  });

  function changeprofiletype(elm) {

    var value = $(elm).val();
    var name = $(elm).attr('name');
    var application_id = $('#application_id').val();;
    var val = 0;
    if (value == 1) {
      $(elm).val(0);
      val = 0
    } else {
      $(elm).val(1);
      val = 1
    }
    var url = APP_URL + '/saveassub';
    $.ajaxSetup({
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      }
    });
    $.ajax({
      type: 'POST',
      url: url,
      data: {
        value: val,
        name: name,
        application_id: application_id
      },
      dataType: 'html',
      success: function(data) {


      }
    })







  }

  function changerate(id) {
    var url = APP_URL + '/getratemodel';
    $.ajaxSetup({
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      }
    });
    $.ajax({
      type: 'POST',
      url: url,
      data: {
        id: id
      },
      dataType: 'html',
      success: function(data) {
        $('#appendstatus').html(data);
        $('#staticBackdropLabel').text('Update pay rates');
        $('#statusmodel').modal('show');
      }
    })
  }

  $('body').on('click', '.assign_model_app', function() {

    var type = $(this).attr('data-type');
    var app_id = $(this).attr('data-program_id');
    var title = $(this).attr('data-title');
    var titleName = $(this).attr('data-titleName');

    $.ajaxSetup({
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      }
    });
    var url = APP_URL + '/get_assign_app_model';
    $.ajax({
      type: "POST",
      url: url,
      data: {
        'type': type,
        app_id: app_id,
        titleName: titleName
      },
      dataType: "html",
      success: function(data) {

        $('.commanTitle').html(title);
        $('#commanAppend').html(data);
        $('#commonModel').modal('show');

      }
    });
  })

  function showHideVideoDemo() {
    console.log('here')
    // $('#video_demo').toggleClass('hide')
    var videoContainer = $('#video_demo');
    var video = videoContainer.find('video')[0];

    if (videoContainer.hasClass('hide')) {
      video.currentTime = 0;
    } else {
      video.pause();
      video.currentTime = 0;
    }

    videoContainer.toggleClass('hide');
  }
</script>

<script>
  $(document).ready(function () {
    $('#is_sub').on('change', function () {
      const is_sub = $(this).val();
      const user_id = $('#user_id').val(); // Make sure this input exists

      $.ajax({
        url: '<?php echo e(route("admin.new-instructor.update-sustitute")); ?>',
        type: 'POST',
        data: {
          is_sub: is_sub,
          user_id: user_id,
          _token: '<?php echo e(csrf_token()); ?>'
        },
        beforeSend: function () {
          // Optionally show a loader
        },
        success: function (response) {
          if (response.success) {
            alertify.success('Substitute preference updated successfully.');
          } else {
            alertify.error(response.message || 'Failed to update.');
          }
        },
        error: function (xhr) {
          if (xhr.status === 422) {
            const errorMsg = xhr.responseJSON.message || 'Validation error occurred.';
            alertify.error('Validation error: ' + errorMsg);
          } else {
            alertify.error('An unexpected error occurred.');
          }
        }
      });
    });
  });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/viewdetails.blade.php ENDPATH**/ ?>